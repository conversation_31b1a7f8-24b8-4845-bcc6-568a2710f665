<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('webhook_endpoints', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('url');
            $table->json('events'); // 订阅的事件类型
            $table->json('headers')->nullable(); // 自定义HTTP头
            $table->boolean('is_active')->default(true);
            $table->string('secret')->nullable(); // 签名密钥
            $table->timestamp('last_delivered_at')->nullable(); // 最后发送时间

            // 绑定到 workspace 而不是 user
            $table->foreignId('workspace_id')->constrained()->onDelete('cascade');

            $table->timestamps();

            $table->index(['workspace_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('webhook_endpoints');
    }
};
