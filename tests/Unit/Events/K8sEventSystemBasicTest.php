<?php

namespace Tests\Unit\Events;

use App\Events\K8s\DeploymentCreated;
use App\Events\K8s\DeploymentDeleted;
use App\Events\K8s\DeploymentUpdated;
use App\Events\K8s\PodCreated;
use App\Events\K8s\ServiceCreated;
use Tests\TestCase;

class K8sEventSystemBasicTest extends TestCase
{
    private string $namespace;

    private array $sampleDeployment;

    protected function setUp(): void
    {
        parent::setUp();

        $this->namespace = 'ns-test';

        $this->sampleDeployment = [
            'apiVersion' => 'apps/v1',
            'kind' => 'Deployment',
            'metadata' => [
                'name' => 'test-deployment',
                'namespace' => $this->namespace,
                'uid' => 'test-uid-123',
            ],
            'spec' => [
                'replicas' => 3,
                'selector' => [
                    'matchLabels' => [
                        'app' => 'test-app',
                    ],
                ],
            ],
        ];
    }

    public function test_deployment_created_event_properties()
    {
        $event = new DeploymentCreated(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $this->sampleDeployment
        );

        $this->assertEquals($this->namespace, $event->namespace);
        $this->assertEquals('test-cluster', $event->clusterName);
        $this->assertEquals(1, $event->clusterId);
        $this->assertEquals('deployment', $event->resourceType);
        $this->assertEquals('test-deployment', $event->resourceName);
        $this->assertEquals('created', $event->action);
        $this->assertEquals($this->sampleDeployment, $event->resource);
    }

    public function test_deployment_updated_event_with_previous_resource()
    {
        $previousResource = $this->sampleDeployment;
        $previousResource['spec']['replicas'] = 2;

        $updatedResource = $this->sampleDeployment;
        $updatedResource['spec']['replicas'] = 5;

        $event = new DeploymentUpdated(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $updatedResource,
            $previousResource
        );

        $this->assertEquals('updated', $event->action);
        $this->assertEquals($updatedResource, $event->resource);
        $this->assertEquals($previousResource, $event->previousResource);
        $this->assertEquals(5, $event->resource['spec']['replicas']);
        $this->assertEquals(2, $event->previousResource['spec']['replicas']);
    }

    public function test_deployment_deleted_event_properties()
    {
        $event = new DeploymentDeleted(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $this->sampleDeployment
        );

        $this->assertEquals('deleted', $event->action);
        $this->assertEquals($this->sampleDeployment, $event->resource);
    }

    public function test_broadcast_channels_are_correct()
    {
        $event = new DeploymentCreated(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $this->sampleDeployment
        );

        $channels = $event->broadcastOn();

        $this->assertCount(2, $channels);
        $this->assertEquals("private-workspace.{$this->namespace}.deployment", $channels[0]->name);
        $this->assertEquals("private-workspace.{$this->namespace}.resources", $channels[1]->name);
    }

    public function test_broadcast_event_names()
    {
        $createdEvent = new DeploymentCreated(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $this->sampleDeployment
        );

        $updatedEvent = new DeploymentUpdated(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $this->sampleDeployment,
            []
        );

        $deletedEvent = new DeploymentDeleted(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $this->sampleDeployment
        );

        $this->assertEquals('deployment.created', $createdEvent->broadcastAs());
        $this->assertEquals('deployment.updated', $updatedEvent->broadcastAs());
        $this->assertEquals('deployment.deleted', $deletedEvent->broadcastAs());
    }

    public function test_broadcast_data_structure()
    {
        $event = new DeploymentCreated(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $this->sampleDeployment
        );

        $broadcastData = $event->broadcastWith();

        // Test required fields
        $this->assertEquals($this->namespace, $broadcastData['namespace']);
        $this->assertEquals(1, $broadcastData['cluster']['id']);
        $this->assertEquals('test-cluster', $broadcastData['cluster']['name']);
        $this->assertEquals('deployment', $broadcastData['resource_type']);
        $this->assertEquals('test-deployment', $broadcastData['resource_name']);
        $this->assertEquals('created', $broadcastData['action']);
        $this->assertEquals($this->sampleDeployment, $broadcastData['resource']);
        $this->assertArrayHasKey('timestamp', $broadcastData);
    }

    public function test_updated_event_broadcast_includes_previous_resource()
    {
        $previousResource = $this->sampleDeployment;
        $previousResource['spec']['replicas'] = 2;

        $event = new DeploymentUpdated(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $this->sampleDeployment,
            $previousResource
        );

        $broadcastData = $event->broadcastWith();

        $this->assertArrayHasKey('previous_resource', $broadcastData);
        $this->assertEquals($previousResource, $broadcastData['previous_resource']);
    }

    public function test_different_resource_types_have_correct_resource_type()
    {
        $sampleService = [
            'metadata' => ['name' => 'test-service'],
            'spec' => ['type' => 'ClusterIP'],
        ];

        $samplePod = [
            'metadata' => ['name' => 'test-pod'],
            'spec' => ['containers' => []],
        ];

        $deploymentEvent = new DeploymentCreated(
            $this->namespace,
            'test-cluster',
            1,
            'test-deployment',
            $this->sampleDeployment
        );

        $serviceEvent = new ServiceCreated(
            $this->namespace,
            'test-cluster',
            1,
            'test-service',
            $sampleService
        );

        $podEvent = new PodCreated(
            $this->namespace,
            'test-cluster',
            1,
            'test-pod',
            $samplePod
        );

        $this->assertEquals('deployment', $deploymentEvent->resourceType);
        $this->assertEquals('service', $serviceEvent->resourceType);
        $this->assertEquals('pod', $podEvent->resourceType);

        $this->assertEquals('deployment.created', $deploymentEvent->broadcastAs());
        $this->assertEquals('service.created', $serviceEvent->broadcastAs());
        $this->assertEquals('pod.created', $podEvent->broadcastAs());
    }

    public function test_resource_name_extracted_from_metadata()
    {
        $resourceWithLongName = [
            'metadata' => [
                'name' => 'very-long-deployment-name-with-hyphens',
                'namespace' => $this->namespace,
            ],
        ];

        $event = new DeploymentCreated(
            $this->namespace,
            'test-cluster',
            1,
            'very-long-deployment-name-with-hyphens',
            $resourceWithLongName
        );

        $this->assertEquals('very-long-deployment-name-with-hyphens', $event->resourceName);
    }
}
