<?php

namespace Tests\Unit\Events;

use App\Events\K8s\DeploymentCreated;
use App\Events\K8s\DeploymentDeleted;
use App\Events\K8s\DeploymentUpdated;
use App\Events\K8s\PodCreated;
use App\Events\K8s\ServiceCreated;
use App\Events\ResourceChanged;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class ResourceChangedWithK8sEventsTest extends TestCase
{
    private string $namespace;

    private string $clusterName;

    private int $clusterId;

    private array $sampleDeployment;

    protected function setUp(): void
    {
        parent::setUp();

        $this->namespace = 'ns-test';
        $this->clusterName = 'test-cluster';
        $this->clusterId = 1;

        $this->sampleDeployment = [
            'apiVersion' => 'apps/v1',
            'kind' => 'Deployment',
            'metadata' => [
                'name' => 'test-deployment',
                'namespace' => $this->namespace,
                'uid' => 'test-uid-123',
            ],
            'spec' => [
                'replicas' => 3,
            ],
        ];
    }

    public function test_resource_changed_triggers_deployment_created_event()
    {
        Event::fake();

        // Create ResourceChanged event with created deployment
        new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'deployment',
            [
                'created' => [$this->sampleDeployment],
            ]
        );

        // Assert that DeploymentCreated was dispatched
        Event::assertDispatched(DeploymentCreated::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->clusterName === $this->clusterName
                && $event->clusterId === $this->clusterId
                && $event->resourceType === 'deployment'
                && $event->resourceName === 'test-deployment'
                && $event->action === 'created';
        });
    }

    public function test_resource_changed_triggers_deployment_updated_event()
    {
        Event::fake();

        $updatedDeployment = $this->sampleDeployment;
        $updatedDeployment['spec']['replicas'] = 5;
        $updatedDeployment['_previous'] = $this->sampleDeployment;

        new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'deployment',
            [
                'updated' => [$updatedDeployment],
            ]
        );

        Event::assertDispatched(DeploymentUpdated::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->action === 'updated'
                && $event->resource['spec']['replicas'] === 5
                && $event->previousResource['spec']['replicas'] === 3;
        });
    }

    public function test_resource_changed_triggers_deployment_deleted_event()
    {
        Event::fake();

        new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'deployment',
            [
                'deleted' => [$this->sampleDeployment],
            ]
        );

        Event::assertDispatched(DeploymentDeleted::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->action === 'deleted'
                && $event->resourceName === 'test-deployment';
        });
    }

    public function test_resource_changed_triggers_multiple_events_in_batch()
    {
        Event::fake();

        $deployment2 = $this->sampleDeployment;
        $deployment2['metadata']['name'] = 'test-deployment-2';

        $deployment3 = $this->sampleDeployment;
        $deployment3['metadata']['name'] = 'test-deployment-3';

        new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'deployment',
            [
                'created' => [$this->sampleDeployment, $deployment2],
                'deleted' => [$deployment3],
            ]
        );

        // Should dispatch multiple specific events
        Event::assertDispatchedTimes(DeploymentCreated::class, 2);
        Event::assertDispatchedTimes(DeploymentDeleted::class, 1);
    }

    public function test_resource_changed_works_with_different_resource_types()
    {
        Event::fake();

        $sampleService = [
            'metadata' => ['name' => 'test-service'],
            'spec' => ['type' => 'ClusterIP'],
        ];

        new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'service',
            [
                'created' => [$sampleService],
            ]
        );

        Event::assertDispatched(ServiceCreated::class, function ($event) {
            return $event->resourceType === 'service'
                && $event->resourceName === 'test-service';
        });
    }

    public function test_resource_changed_works_with_plural_resource_types()
    {
        Event::fake();

        // Test with plural form 'deployments'
        new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'deployments',
            [
                'created' => [$this->sampleDeployment],
            ]
        );

        Event::assertDispatched(DeploymentCreated::class);

        Event::fake(); // Reset

        // Test with plural form 'services'
        $sampleService = [
            'metadata' => ['name' => 'test-service'],
            'spec' => ['type' => 'ClusterIP'],
        ];

        new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'services',
            [
                'created' => [$sampleService],
            ]
        );

        Event::assertDispatched(ServiceCreated::class);
    }

    public function test_resource_changed_handles_unknown_resource_type_gracefully()
    {
        Event::fake();

        // This should not throw an error
        new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'unknown-resource-type',
            [
                'created' => [$this->sampleDeployment],
            ]
        );

        // No specific events should be dispatched for unknown types
        Event::assertNotDispatched(DeploymentCreated::class);
        Event::assertNotDispatched(ServiceCreated::class);
        Event::assertNotDispatched(PodCreated::class);
    }

    public function test_resource_changed_handles_malformed_resource_gracefully()
    {
        Event::fake();

        $malformedResource = [
            // Missing metadata.name
            'spec' => ['replicas' => 3],
        ];

        // This should not throw an error
        new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'deployment',
            [
                'created' => [$malformedResource],
            ]
        );

        // Should still try to dispatch the event with 'unknown' name
        Event::assertDispatched(DeploymentCreated::class, function ($event) {
            return $event->resourceName === 'unknown';
        });
    }

    public function test_resource_changed_preserves_original_functionality()
    {
        // Test that ResourceChanged still works as before
        $changes = [
            'created' => [$this->sampleDeployment],
            'updated' => [],
            'deleted' => [],
        ];

        $event = new ResourceChanged(
            $this->namespace,
            $this->clusterName,
            $this->clusterId,
            'deployment',
            $changes
        );

        // Original properties should still work
        $this->assertEquals($this->namespace, $event->namespace);
        $this->assertEquals($this->clusterName, $event->clusterName);
        $this->assertEquals($this->clusterId, $event->clusterId);
        $this->assertEquals('deployment', $event->resourceType);
        $this->assertEquals($changes, $event->changes);
        $this->assertEquals(1, $event->summary['created_count']);
        $this->assertEquals(0, $event->summary['updated_count']);
        $this->assertEquals(0, $event->summary['deleted_count']);
        $this->assertEquals(1, $event->summary['total_changes']);

        // Broadcast functionality should still work
        $this->assertEquals('resource.changed', $event->broadcastAs());

        $channels = $event->broadcastOn();
        $this->assertCount(1, $channels);
        $this->assertEquals("private-workspace.{$this->namespace}.resources", $channels[0]->name);

        $broadcastData = $event->broadcastWith();
        $this->assertEquals($this->namespace, $broadcastData['namespace']);
        $this->assertEquals($this->clusterId, $broadcastData['cluster']['id']);
        $this->assertEquals($this->clusterName, $broadcastData['cluster']['name']);
        $this->assertEquals('deployment', $broadcastData['resource_type']);
        $this->assertEquals($changes, $broadcastData['changes']);
        $this->assertArrayHasKey('timestamp', $broadcastData);
    }
}
