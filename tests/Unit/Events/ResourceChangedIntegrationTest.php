<?php

namespace Tests\Unit\Events;

use App\Events\K8s\DeploymentCreated;
use App\Events\K8s\DeploymentDeleted;
use App\Events\K8s\DeploymentUpdated;
use App\Events\K8s\K8sEventFactory;
use App\Events\K8s\ResourceChangedDispatcher;
use App\Events\K8s\ServiceCreated;
use App\Events\ResourceChanged;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class ResourceChangedIntegrationTest extends TestCase
{
    private $mockCluster;

    private string $namespace;

    private array $sampleDeployment;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a simple mock cluster object
        $this->mockCluster = new class
        {
            public $id = 1;

            public $name = 'test-cluster';
        };

        $this->namespace = 'ns-test';

        $this->sampleDeployment = [
            'apiVersion' => 'apps/v1',
            'kind' => 'Deployment',
            'metadata' => [
                'name' => 'test-deployment',
                'namespace' => $this->namespace,
                'uid' => 'test-uid-123',
            ],
            'spec' => [
                'replicas' => 3,
            ],
        ];
    }

    public function test_k8s_event_factory_creates_correct_events()
    {
        // Test creating different event types
        $createdEvent = K8sEventFactory::create(
            'deployment',
            'created',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment
        );

        $updatedEvent = K8sEventFactory::create(
            'deployment',
            'updated',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment,
            ['spec' => ['replicas' => 2]]
        );

        $deletedEvent = K8sEventFactory::create(
            'deployment',
            'deleted',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment
        );

        $this->assertInstanceOf(DeploymentCreated::class, $createdEvent);
        $this->assertInstanceOf(DeploymentUpdated::class, $updatedEvent);
        $this->assertInstanceOf(DeploymentDeleted::class, $deletedEvent);
    }

    public function test_k8s_event_factory_returns_null_for_invalid_inputs()
    {
        // Test unsupported resource type
        $event1 = K8sEventFactory::create(
            'invalid-resource',
            'created',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment
        );

        // Test unsupported action
        $event2 = K8sEventFactory::create(
            'deployment',
            'invalid-action',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment
        );

        $this->assertNull($event1);
        $this->assertNull($event2);
    }

    public function test_k8s_event_factory_works_with_different_resource_types()
    {
        $sampleService = [
            'metadata' => ['name' => 'test-service'],
            'spec' => ['type' => 'ClusterIP'],
        ];

        $serviceEvent = K8sEventFactory::create(
            'service',
            'created',
            $this->namespace,
            $this->mockCluster,
            $sampleService
        );

        $this->assertInstanceOf(ServiceCreated::class, $serviceEvent);
        $this->assertEquals('service', $serviceEvent->resourceType);
        $this->assertEquals('test-service', $serviceEvent->resourceName);
    }

    public function test_resource_changed_dispatcher_single_dispatch_fires_both_events()
    {
        Event::fake();

        ResourceChangedDispatcher::dispatchSingle(
            $this->namespace,
            $this->mockCluster,
            'deployment',
            'created',
            $this->sampleDeployment
        );

        // Verify ResourceChanged event was dispatched
        Event::assertDispatched(ResourceChanged::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->clusterName === 'test-cluster'
                && $event->clusterId === 1
                && $event->resourceType === 'deployment'
                && isset($event->changes['created'])
                && count($event->changes['created']) === 1
                && $event->changes['created'][0]['metadata']['name'] === 'test-deployment';
        });

        // Verify specific DeploymentCreated event was dispatched
        Event::assertDispatched(DeploymentCreated::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->clusterName === 'test-cluster'
                && $event->resourceType === 'deployment'
                && $event->resourceName === 'test-deployment'
                && $event->action === 'created';
        });
    }

    public function test_resource_changed_dispatcher_batch_dispatch()
    {
        Event::fake();

        $deployment2 = $this->sampleDeployment;
        $deployment2['metadata']['name'] = 'test-deployment-2';

        $deployment3 = $this->sampleDeployment;
        $deployment3['metadata']['name'] = 'test-deployment-3';

        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->mockCluster,
            'deployment',
            [
                'created' => [$this->sampleDeployment, $deployment2],
                'deleted' => [$deployment3],
            ]
        );

        // Verify one ResourceChanged event with multiple changes
        Event::assertDispatchedTimes(ResourceChanged::class, 1);
        Event::assertDispatched(ResourceChanged::class, function ($event) {
            return count($event->changes['created']) === 2
                && count($event->changes['deleted']) === 1
                && $event->summary['total_changes'] === 3;
        });

        // Verify individual specific events
        Event::assertDispatchedTimes(DeploymentCreated::class, 2);
        Event::assertDispatchedTimes(DeploymentDeleted::class, 1);
    }

    public function test_resource_changed_dispatcher_handles_updated_with_previous_data()
    {
        Event::fake();

        $updatedDeployment = $this->sampleDeployment;
        $updatedDeployment['spec']['replicas'] = 5;
        $updatedDeployment['_previous'] = $this->sampleDeployment;

        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->mockCluster,
            'deployment',
            [
                'updated' => [$updatedDeployment],
            ]
        );

        Event::assertDispatched(DeploymentUpdated::class, function ($event) {
            return $event->resource['spec']['replicas'] === 5
                && $event->previousResource['spec']['replicas'] === 3;
        });
    }

    public function test_resource_changed_dispatcher_gracefully_handles_unsupported_resource()
    {
        Event::fake();

        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->mockCluster,
            'unsupported-resource-type',
            [
                'created' => [$this->sampleDeployment],
            ]
        );

        // ResourceChanged should still be dispatched
        Event::assertDispatched(ResourceChanged::class);

        // But no specific K8s events should be dispatched
        Event::assertNotDispatched(DeploymentCreated::class);
    }

    public function test_k8s_event_factory_get_supported_resource_types()
    {
        $supportedTypes = K8sEventFactory::getSupportedResourceTypes();

        $expectedTypes = [
            'deployment',
            'statefulset',
            'service',
            'ingress',
            'pod',
            'secret',
            'configmap',
            'persistentvolumeclaim',
            'horizontalpodautoscaler',
            'event',
        ];

        foreach ($expectedTypes as $type) {
            $this->assertContains($type, $supportedTypes, "Resource type '{$type}' should be supported");
        }
    }

    public function test_k8s_event_factory_get_supported_actions()
    {
        $supportedActions = K8sEventFactory::getSupportedActions('deployment');

        $expectedActions = ['created', 'updated', 'deleted', 'changed'];

        foreach ($expectedActions as $action) {
            $this->assertContains($action, $supportedActions, "Action '{$action}' should be supported for deployment");
        }

        // Test unsupported resource type
        $unsupportedActions = K8sEventFactory::getSupportedActions('unsupported-resource');
        $this->assertEmpty($unsupportedActions);
    }

    public function test_event_maintains_resource_metadata()
    {
        $complexDeployment = [
            'apiVersion' => 'apps/v1',
            'kind' => 'Deployment',
            'metadata' => [
                'name' => 'complex-deployment',
                'namespace' => $this->namespace,
                'uid' => 'complex-uid-456',
                'labels' => [
                    'app' => 'my-app',
                    'version' => 'v1.0.0',
                ],
                'annotations' => [
                    'deployment.kubernetes.io/revision' => '1',
                ],
            ],
            'spec' => [
                'replicas' => 3,
                'selector' => [
                    'matchLabels' => [
                        'app' => 'my-app',
                    ],
                ],
            ],
        ];

        $event = K8sEventFactory::create(
            'deployment',
            'created',
            $this->namespace,
            $this->mockCluster,
            $complexDeployment
        );

        $this->assertEquals('complex-deployment', $event->resourceName);
        $this->assertEquals($complexDeployment, $event->resource);

        $broadcastData = $event->broadcastWith();
        $this->assertEquals($complexDeployment, $broadcastData['resource']);
        $this->assertEquals('my-app', $broadcastData['resource']['metadata']['labels']['app']);
    }
}
