<?php

namespace Tests\Unit\Events;

use App\Events\K8s\DeploymentCreated;
use App\Events\K8s\DeploymentDeleted;
use App\Events\K8s\DeploymentUpdated;
use App\Events\K8s\K8sEventFactory;
use App\Events\K8s\ResourceChangedDispatcher;
use App\Events\ResourceChanged;
use App\Models\Cluster;
use Illuminate\Support\Facades\Event;
use Mockery;
use Tests\TestCase;

class K8sEventSystemUnitTest extends TestCase
{
    private $mockCluster;

    private string $namespace;

    private array $sampleDeployment;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a simple mock object for Cluster
        $this->mockCluster = new class
        {
            public $id = 1;

            public $name = 'test-cluster';
        };

        $this->namespace = 'ns-test';

        $this->sampleDeployment = [
            'apiVersion' => 'apps/v1',
            'kind' => 'Deployment',
            'metadata' => [
                'name' => 'test-deployment',
                'namespace' => $this->namespace,
                'uid' => 'test-uid-123',
            ],
            'spec' => [
                'replicas' => 3,
                'selector' => [
                    'matchLabels' => [
                        'app' => 'test-app',
                    ],
                ],
                'template' => [
                    'metadata' => [
                        'labels' => [
                            'app' => 'test-app',
                        ],
                    ],
                    'spec' => [
                        'containers' => [
                            [
                                'name' => 'test-container',
                                'image' => 'nginx:latest',
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    public function test_k8s_event_factory_creates_deployment_created_event()
    {
        $event = K8sEventFactory::create(
            'deployment',
            'created',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment
        );

        $this->assertInstanceOf(DeploymentCreated::class, $event);
        $this->assertEquals($this->namespace, $event->namespace);
        $this->assertEquals('test-cluster', $event->clusterName);
        $this->assertEquals(1, $event->clusterId);
        $this->assertEquals('deployment', $event->resourceType);
        $this->assertEquals('test-deployment', $event->resourceName);
        $this->assertEquals('created', $event->action);
    }

    public function test_k8s_event_factory_creates_deployment_updated_event()
    {
        $previousResource = $this->sampleDeployment;
        $previousResource['spec']['replicas'] = 2;

        $event = K8sEventFactory::create(
            'deployment',
            'updated',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment,
            $previousResource
        );

        $this->assertInstanceOf(DeploymentUpdated::class, $event);
        $this->assertEquals('updated', $event->action);
        $this->assertEquals($previousResource, $event->previousResource);
        $this->assertEquals(3, $event->resource['spec']['replicas']);
        $this->assertEquals(2, $event->previousResource['spec']['replicas']);
    }

    public function test_k8s_event_factory_creates_deployment_deleted_event()
    {
        $event = K8sEventFactory::create(
            'deployment',
            'deleted',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment
        );

        $this->assertInstanceOf(DeploymentDeleted::class, $event);
        $this->assertEquals('deleted', $event->action);
    }

    public function test_k8s_event_factory_returns_null_for_unsupported_resource()
    {
        $event = K8sEventFactory::create(
            'unsupported-resource',
            'created',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment
        );

        $this->assertNull($event);
    }

    public function test_k8s_event_factory_returns_null_for_unsupported_action()
    {
        $event = K8sEventFactory::create(
            'deployment',
            'unsupported-action',
            $this->namespace,
            $this->mockCluster,
            $this->sampleDeployment
        );

        $this->assertNull($event);
    }

    public function test_resource_changed_dispatcher_single_dispatch()
    {
        Event::fake();

        ResourceChangedDispatcher::dispatchSingle(
            $this->namespace,
            $this->mockCluster,
            'deployment',
            'created',
            $this->sampleDeployment
        );

        // Assert ResourceChanged event was dispatched
        Event::assertDispatched(ResourceChanged::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->clusterName === 'test-cluster'
                && $event->clusterId === 1
                && $event->resourceType === 'deployment'
                && isset($event->changes['created'])
                && count($event->changes['created']) === 1;
        });

        // Assert DeploymentCreated event was dispatched
        Event::assertDispatched(DeploymentCreated::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->clusterName === 'test-cluster'
                && $event->resourceType === 'deployment'
                && $event->resourceName === 'test-deployment'
                && $event->action === 'created';
        });
    }

    public function test_resource_changed_dispatcher_batch_dispatch()
    {
        Event::fake();

        $deployment2 = $this->sampleDeployment;
        $deployment2['metadata']['name'] = 'test-deployment-2';

        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->mockCluster,
            'deployment',
            [
                'created' => [$this->sampleDeployment, $deployment2],
                'deleted' => [$this->sampleDeployment],
            ]
        );

        // Assert ResourceChanged event was dispatched once
        Event::assertDispatchedTimes(ResourceChanged::class, 1);

        // Assert specific events were dispatched
        Event::assertDispatchedTimes(DeploymentCreated::class, 2);
        Event::assertDispatchedTimes(DeploymentDeleted::class, 1);

        // Check ResourceChanged event details
        Event::assertDispatched(ResourceChanged::class, function ($event) {
            return count($event->changes['created']) === 2
                && count($event->changes['deleted']) === 1
                && $event->summary['total_changes'] === 3;
        });
    }

    public function test_deployment_created_event_broadcast_properties()
    {
        $event = new DeploymentCreated(
            $this->namespace,
            $this->mockCluster->name,
            $this->mockCluster->id,
            'test-deployment',
            $this->sampleDeployment
        );

        // Test broadcast channels
        $channels = $event->broadcastOn();
        $this->assertCount(2, $channels);
        $this->assertEquals("workspace.{$this->namespace}.deployment", $channels[0]->name);
        $this->assertEquals("workspace.{$this->namespace}.resources", $channels[1]->name);

        // Test broadcast event name
        $this->assertEquals('deployment.created', $event->broadcastAs());

        // Test broadcast data
        $broadcastData = $event->broadcastWith();
        $this->assertEquals($this->namespace, $broadcastData['namespace']);
        $this->assertEquals($this->mockCluster->id, $broadcastData['cluster']['id']);
        $this->assertEquals($this->mockCluster->name, $broadcastData['cluster']['name']);
        $this->assertEquals('deployment', $broadcastData['resource_type']);
        $this->assertEquals('test-deployment', $broadcastData['resource_name']);
        $this->assertEquals('created', $broadcastData['action']);
        $this->assertArrayHasKey('resource', $broadcastData);
        $this->assertArrayHasKey('timestamp', $broadcastData);
    }

    public function test_deployment_updated_event_includes_previous_resource()
    {
        $previousResource = $this->sampleDeployment;
        $previousResource['spec']['replicas'] = 2;

        $event = new DeploymentUpdated(
            $this->namespace,
            $this->mockCluster->name,
            $this->mockCluster->id,
            'test-deployment',
            $this->sampleDeployment,
            $previousResource
        );

        $broadcastData = $event->broadcastWith();
        $this->assertArrayHasKey('previous_resource', $broadcastData);
        $this->assertEquals($previousResource, $broadcastData['previous_resource']);
        $this->assertEquals(2, $broadcastData['previous_resource']['spec']['replicas']);
        $this->assertEquals(3, $broadcastData['resource']['spec']['replicas']);
    }

    public function test_resource_changed_handles_updated_with_previous_data()
    {
        Event::fake();

        $updatedDeployment = $this->sampleDeployment;
        $updatedDeployment['spec']['replicas'] = 5;

        // Add previous resource data
        $updatedDeployment['_previous'] = $this->sampleDeployment;

        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->mockCluster,
            'deployment',
            [
                'updated' => [$updatedDeployment],
            ]
        );

        Event::assertDispatched(DeploymentUpdated::class, function ($event) {
            return $event->resource['spec']['replicas'] === 5
                && $event->previousResource['spec']['replicas'] === 3;
        });
    }

    public function test_k8s_event_factory_get_supported_methods()
    {
        $supportedTypes = K8sEventFactory::getSupportedResourceTypes();
        $this->assertContains('deployment', $supportedTypes);
        $this->assertContains('service', $supportedTypes);
        $this->assertContains('pod', $supportedTypes);

        $supportedActions = K8sEventFactory::getSupportedActions('deployment');
        $this->assertContains('created', $supportedActions);
        $this->assertContains('updated', $supportedActions);
        $this->assertContains('deleted', $supportedActions);
        $this->assertContains('changed', $supportedActions);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
