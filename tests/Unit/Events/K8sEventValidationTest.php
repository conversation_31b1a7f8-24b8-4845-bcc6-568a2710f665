<?php

namespace Tests\Unit\Events;

use App\Events\K8s\DeploymentCreated;
use App\Events\K8s\DeploymentDeleted;
use App\Events\K8s\DeploymentUpdated;
use App\Events\K8s\ServiceCreated;
use App\Events\ResourceChanged;
use Tests\TestCase;

/**
 * Simple validation test for K8s Event System to verify ResourceChanged integration
 */
class K8sEventValidationTest extends TestCase
{
    public function test_resource_changed_event_can_be_created_and_broadcasted()
    {
        $namespace = 'ns-test';
        $clusterName = 'test-cluster';
        $clusterId = 1;
        $resourceType = 'deployment';

        $sampleDeployment = [
            'metadata' => [
                'name' => 'test-deployment',
                'namespace' => $namespace,
            ],
            'spec' => [
                'replicas' => 3,
            ],
        ];

        $changes = [
            'created' => [$sampleDeployment],
            'updated' => [],
            'deleted' => [],
        ];

        // Test creating ResourceChanged event
        $resourceChangedEvent = new ResourceChanged(
            $namespace,
            $clusterName,
            $clusterId,
            $resourceType,
            $changes
        );

        $this->assertEquals($namespace, $resourceChangedEvent->namespace);
        $this->assertEquals($clusterName, $resourceChangedEvent->clusterName);
        $this->assertEquals($clusterId, $resourceChangedEvent->clusterId);
        $this->assertEquals($resourceType, $resourceChangedEvent->resourceType);
        $this->assertEquals($changes, $resourceChangedEvent->changes);

        // Test summary generation
        $this->assertEquals(1, $resourceChangedEvent->summary['created_count']);
        $this->assertEquals(0, $resourceChangedEvent->summary['updated_count']);
        $this->assertEquals(0, $resourceChangedEvent->summary['deleted_count']);
        $this->assertEquals(1, $resourceChangedEvent->summary['total_changes']);

        // Test broadcast properties
        $this->assertEquals('resource.changed', $resourceChangedEvent->broadcastAs());

        $channels = $resourceChangedEvent->broadcastOn();
        $this->assertCount(1, $channels);
        $this->assertEquals("private-workspace.{$namespace}.resources", $channels[0]->name);

        $broadcastData = $resourceChangedEvent->broadcastWith();
        $this->assertEquals($namespace, $broadcastData['namespace']);
        $this->assertEquals($clusterId, $broadcastData['cluster']['id']);
        $this->assertEquals($clusterName, $broadcastData['cluster']['name']);
        $this->assertEquals($resourceType, $broadcastData['resource_type']);
        $this->assertEquals($changes, $broadcastData['changes']);
        $this->assertArrayHasKey('timestamp', $broadcastData);
    }

    public function test_specific_k8s_events_can_be_created_independently()
    {
        $namespace = 'ns-test';
        $clusterName = 'test-cluster';
        $clusterId = 1;

        $sampleDeployment = [
            'metadata' => [
                'name' => 'test-deployment',
                'namespace' => $namespace,
            ],
            'spec' => [
                'replicas' => 3,
            ],
        ];

        // Test DeploymentCreated event
        $createdEvent = new DeploymentCreated(
            $namespace,
            $clusterName,
            $clusterId,
            'test-deployment',
            $sampleDeployment
        );

        $this->assertEquals('deployment.created', $createdEvent->broadcastAs());
        $this->assertEquals('created', $createdEvent->action);
        $this->assertEquals('deployment', $createdEvent->resourceType);

        // Test DeploymentUpdated event
        $previousResource = $sampleDeployment;
        $previousResource['spec']['replicas'] = 2;

        $updatedDeployment = $sampleDeployment;
        $updatedDeployment['spec']['replicas'] = 5;

        $updatedEvent = new DeploymentUpdated(
            $namespace,
            $clusterName,
            $clusterId,
            'test-deployment',
            $updatedDeployment,
            $previousResource
        );

        $this->assertEquals('deployment.updated', $updatedEvent->broadcastAs());
        $this->assertEquals('updated', $updatedEvent->action);
        $this->assertEquals($previousResource, $updatedEvent->previousResource);

        // Test DeploymentDeleted event
        $deletedEvent = new DeploymentDeleted(
            $namespace,
            $clusterName,
            $clusterId,
            'test-deployment',
            $sampleDeployment
        );

        $this->assertEquals('deployment.deleted', $deletedEvent->broadcastAs());
        $this->assertEquals('deleted', $deletedEvent->action);
    }

    public function test_different_resource_types_work_correctly()
    {
        $namespace = 'ns-test';
        $clusterName = 'test-cluster';
        $clusterId = 1;

        $sampleService = [
            'metadata' => [
                'name' => 'test-service',
                'namespace' => $namespace,
            ],
            'spec' => [
                'type' => 'ClusterIP',
                'ports' => [
                    [
                        'port' => 80,
                        'targetPort' => 8080,
                    ],
                ],
            ],
        ];

        $serviceEvent = new ServiceCreated(
            $namespace,
            $clusterName,
            $clusterId,
            'test-service',
            $sampleService
        );

        $this->assertEquals('service', $serviceEvent->resourceType);
        $this->assertEquals('service.created', $serviceEvent->broadcastAs());
        $this->assertEquals('test-service', $serviceEvent->resourceName);
    }

    public function test_broadcast_channels_include_both_specific_and_general()
    {
        $namespace = 'ns-test';
        $event = new DeploymentCreated(
            $namespace,
            'test-cluster',
            1,
            'test-deployment',
            ['metadata' => ['name' => 'test']]
        );

        $channels = $event->broadcastOn();
        $this->assertCount(2, $channels);

        $channelNames = array_map(fn ($channel) => $channel->name, $channels);
        $this->assertContains("private-workspace.{$namespace}.deployment", $channelNames);
        $this->assertContains("private-workspace.{$namespace}.resources", $channelNames);
    }

    public function test_events_maintain_complete_resource_data()
    {
        $complexResource = [
            'apiVersion' => 'apps/v1',
            'kind' => 'Deployment',
            'metadata' => [
                'name' => 'complex-deployment',
                'namespace' => 'ns-test',
                'uid' => 'complex-uid',
                'labels' => [
                    'app' => 'my-app',
                    'version' => 'v1.0.0',
                ],
                'annotations' => [
                    'deployment.kubernetes.io/revision' => '1',
                ],
                'creationTimestamp' => '2024-01-01T00:00:00Z',
                'resourceVersion' => '12345',
            ],
            'spec' => [
                'replicas' => 3,
                'selector' => [
                    'matchLabels' => [
                        'app' => 'my-app',
                    ],
                ],
                'template' => [
                    'metadata' => [
                        'labels' => [
                            'app' => 'my-app',
                        ],
                    ],
                    'spec' => [
                        'containers' => [
                            [
                                'name' => 'my-container',
                                'image' => 'nginx:latest',
                                'ports' => [
                                    [
                                        'containerPort' => 80,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
            'status' => [
                'replicas' => 3,
                'readyReplicas' => 3,
                'availableReplicas' => 3,
            ],
        ];

        $event = new DeploymentCreated(
            'ns-test',
            'test-cluster',
            1,
            'complex-deployment',
            $complexResource
        );

        // Verify the complete resource is preserved
        $this->assertEquals($complexResource, $event->resource);

        $broadcastData = $event->broadcastWith();
        $this->assertEquals($complexResource, $broadcastData['resource']);

        // Verify specific nested data is accessible
        $this->assertEquals('my-app', $event->resource['metadata']['labels']['app']);
        $this->assertEquals('nginx:latest', $event->resource['spec']['template']['spec']['containers'][0]['image']);
        $this->assertEquals(3, $event->resource['status']['readyReplicas']);
    }

    public function test_events_are_backwards_compatible_with_original_resource_changed()
    {
        // Test that we can still use ResourceChanged event in the traditional way
        $changes = [
            'created' => [
                [
                    'metadata' => ['name' => 'deployment-1'],
                    'spec' => ['replicas' => 3],
                ],
                [
                    'metadata' => ['name' => 'deployment-2'],
                    'spec' => ['replicas' => 2],
                ],
            ],
            'updated' => [
                [
                    'metadata' => ['name' => 'deployment-3'],
                    'spec' => ['replicas' => 5],
                ],
            ],
            'deleted' => [
                [
                    'metadata' => ['name' => 'deployment-4'],
                    'spec' => ['replicas' => 1],
                ],
            ],
        ];

        $event = new ResourceChanged(
            'ns-test',
            'test-cluster',
            1,
            'deployment',
            $changes
        );

        $this->assertEquals(2, $event->summary['created_count']);
        $this->assertEquals(1, $event->summary['updated_count']);
        $this->assertEquals(1, $event->summary['deleted_count']);
        $this->assertEquals(4, $event->summary['total_changes']);

        // Verify the changes are accessible as before
        $this->assertCount(2, $event->changes['created']);
        $this->assertCount(1, $event->changes['updated']);
        $this->assertCount(1, $event->changes['deleted']);

        $this->assertEquals('deployment-1', $event->changes['created'][0]['metadata']['name']);
        $this->assertEquals('deployment-3', $event->changes['updated'][0]['metadata']['name']);
        $this->assertEquals('deployment-4', $event->changes['deleted'][0]['metadata']['name']);
    }
}
