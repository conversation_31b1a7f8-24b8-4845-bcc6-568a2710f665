<?php

namespace Tests\Unit;

use App\Listeners\WebhookCallEventListener;
use App\Models\WebhookDelivery;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Spa<PERSON>\WebhookServer\Events\FinalWebhookCallFailedEvent;
use Spatie\WebhookServer\Events\WebhookCallFailedEvent;
use Spatie\WebhookServer\Events\WebhookCallSucceededEvent;
use Tests\TestCase;

class WebhookCallEventListenerTest extends TestCase
{
    use RefreshDatabase;

    private WebhookCallEventListener $listener;

    protected function setUp(): void
    {
        parent::setUp();
        $this->listener = new WebhookCallEventListener;
    }

    #[Test]
    public function it_can_handle_webhook_call_succeeded_event()
    {
        // 创建一个模拟的 WebhookDelivery
        $delivery = Mockery::mock(WebhookDelivery::class);
        $delivery->shouldReceive('update')->once()->with(Mockery::type('array'));

        // 模拟 WebhookDelivery::find() 方法
        WebhookDelivery::shouldReceive('find')
            ->with(123)
            ->once()
            ->andReturn($delivery);

        // 创建成功事件
        $event = new WebhookCallSucceededEvent(
            httpVerb: 'post',
            webhookUrl: 'https://example.com/webhook',
            payload: ['test' => 'data'],
            headers: ['Content-Type' => 'application/json'],
            meta: [
                'delivery_id' => 123,
                'endpoint_id' => 456,
                'event_type' => 'test.event',
            ],
            tags: [],
            uuid: 'test-uuid',
            attempt: 1,
            response: new \GuzzleHttp\Psr7\Response(200, [], '{"status":"ok"}')
        );

        // 执行处理方法
        $this->listener->handleWebhookCallSucceeded($event);

        // 验证没有异常抛出
        $this->assertTrue(true);
    }

    #[Test]
    public function it_can_handle_webhook_call_failed_event()
    {
        // 创建一个模拟的 WebhookDelivery
        $delivery = Mockery::mock(WebhookDelivery::class);
        $delivery->shouldReceive('update')->once()->with(Mockery::type('array'));

        // 模拟 WebhookDelivery::find() 方法
        WebhookDelivery::shouldReceive('find')
            ->with(123)
            ->once()
            ->andReturn($delivery);

        // 创建失败事件
        $event = new WebhookCallFailedEvent(
            httpVerb: 'post',
            webhookUrl: 'https://example.com/webhook',
            payload: ['test' => 'data'],
            headers: ['Content-Type' => 'application/json'],
            meta: [
                'delivery_id' => 123,
                'endpoint_id' => 456,
                'event_type' => 'test.event',
            ],
            tags: [],
            uuid: 'test-uuid',
            attempt: 1,
            response: new \GuzzleHttp\Psr7\Response(500, [], '{"error":"Internal Server Error"}')
        );

        // 执行处理方法
        $this->listener->handleWebhookCallFailed($event);

        // 验证没有异常抛出
        $this->assertTrue(true);
    }

    #[Test]
    public function it_can_handle_final_webhook_call_failed_event()
    {
        // 创建一个模拟的 WebhookDelivery
        $delivery = Mockery::mock(WebhookDelivery::class);
        $delivery->shouldReceive('update')->once()->with(Mockery::type('array'));

        // 模拟 WebhookDelivery::find() 方法
        WebhookDelivery::shouldReceive('find')
            ->with(123)
            ->once()
            ->andReturn($delivery);

        // 创建最终失败事件
        $event = new FinalWebhookCallFailedEvent(
            httpVerb: 'post',
            webhookUrl: 'https://example.com/webhook',
            payload: ['test' => 'data'],
            headers: ['Content-Type' => 'application/json'],
            meta: [
                'delivery_id' => 123,
                'endpoint_id' => 456,
                'event_type' => 'test.event',
            ],
            tags: [],
            uuid: 'test-uuid',
            attempt: 3,
            response: new \GuzzleHttp\Psr7\Response(500, [], '{"error":"Internal Server Error"}')
        );

        // 执行处理方法
        $this->listener->handleFinalWebhookCallFailed($event);

        // 验证没有异常抛出
        $this->assertTrue(true);
    }

    #[Test]
    public function it_handles_missing_delivery_id_gracefully()
    {
        // 创建没有 delivery_id 的事件
        $event = new WebhookCallSucceededEvent(
            httpVerb: 'post',
            webhookUrl: 'https://example.com/webhook',
            payload: ['test' => 'data'],
            headers: ['Content-Type' => 'application/json'],
            meta: [
                'endpoint_id' => 456,
                'event_type' => 'test.event',
            ],
            tags: [],
            uuid: 'test-uuid',
            attempt: 1,
            response: new \GuzzleHttp\Psr7\Response(200, [], '{"status":"ok"}')
        );

        // 执行处理方法 - 应该不会抛出异常
        $this->listener->handleWebhookCallSucceeded($event);

        // 验证没有异常抛出
        $this->assertTrue(true);
    }

    #[Test]
    public function it_handles_missing_delivery_record_gracefully()
    {
        // 模拟 WebhookDelivery::find() 返回 null
        WebhookDelivery::shouldReceive('find')
            ->with(999)
            ->once()
            ->andReturn(null);

        // 创建事件
        $event = new WebhookCallSucceededEvent(
            httpVerb: 'post',
            webhookUrl: 'https://example.com/webhook',
            payload: ['test' => 'data'],
            headers: ['Content-Type' => 'application/json'],
            meta: [
                'delivery_id' => 999,
                'endpoint_id' => 456,
                'event_type' => 'test.event',
            ],
            tags: [],
            uuid: 'test-uuid',
            attempt: 1,
            response: new \GuzzleHttp\Psr7\Response(200, [], '{"status":"ok"}')
        );

        // 执行处理方法 - 应该不会抛出异常
        $this->listener->handleWebhookCallSucceeded($event);

        // 验证没有异常抛出
        $this->assertTrue(true);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
