<?php

namespace Tests\Feature\Events;

use App\Events\K8s\DeploymentCreated;
use App\Events\K8s\DeploymentDeleted;
use App\Events\K8s\DeploymentUpdated;
use App\Events\K8s\ResourceChangedDispatcher;
use App\Events\ResourceChanged;
use App\Models\Cluster;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class K8sEventSystemTest extends TestCase
{
    use RefreshDatabase;

    private Cluster $cluster;

    private string $namespace;

    private array $sampleDeployment;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test cluster
        $this->cluster = Cluster::factory()->create([
            'name' => 'test-cluster',
        ]);

        $this->namespace = 'ns-test';

        $this->sampleDeployment = [
            'apiVersion' => 'apps/v1',
            'kind' => 'Deployment',
            'metadata' => [
                'name' => 'test-deployment',
                'namespace' => $this->namespace,
                'uid' => 'test-uid-123',
            ],
            'spec' => [
                'replicas' => 3,
                'selector' => [
                    'matchLabels' => [
                        'app' => 'test-app',
                    ],
                ],
                'template' => [
                    'metadata' => [
                        'labels' => [
                            'app' => 'test-app',
                        ],
                    ],
                    'spec' => [
                        'containers' => [
                            [
                                'name' => 'test-container',
                                'image' => 'nginx:latest',
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    public function test_resource_changed_event_triggers_deployment_created_event()
    {
        Event::fake();

        // Test ResourceChangedDispatcher.dispatch() with created deployment
        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->cluster,
            'deployment',
            [
                'created' => [$this->sampleDeployment],
            ]
        );

        // Assert both ResourceChanged and DeploymentCreated events were dispatched
        Event::assertDispatched(ResourceChanged::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->clusterName === $this->cluster->name
                && $event->resourceType === 'deployment'
                && isset($event->changes['created'])
                && count($event->changes['created']) === 1;
        });

        Event::assertDispatched(DeploymentCreated::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->clusterName === $this->cluster->name
                && $event->resourceType === 'deployment'
                && $event->resourceName === 'test-deployment'
                && $event->action === 'created';
        });
    }

    public function test_resource_changed_event_triggers_deployment_updated_event()
    {
        Event::fake();

        $updatedDeployment = $this->sampleDeployment;
        $updatedDeployment['spec']['replicas'] = 5;
        $updatedDeployment['_previous'] = $this->sampleDeployment; // Simulate previous version

        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->cluster,
            'deployment',
            [
                'updated' => [$updatedDeployment],
            ]
        );

        Event::assertDispatched(ResourceChanged::class);
        Event::assertDispatched(DeploymentUpdated::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->action === 'updated'
                && $event->resource['spec']['replicas'] === 5
                && $event->previousResource['spec']['replicas'] === 3;
        });
    }

    public function test_resource_changed_event_triggers_deployment_deleted_event()
    {
        Event::fake();

        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->cluster,
            'deployment',
            [
                'deleted' => [$this->sampleDeployment],
            ]
        );

        Event::assertDispatched(ResourceChanged::class);
        Event::assertDispatched(DeploymentDeleted::class, function ($event) {
            return $event->namespace === $this->namespace
                && $event->action === 'deleted'
                && $event->resourceName === 'test-deployment';
        });
    }

    public function test_resource_changed_dispatcher_single_dispatch()
    {
        Event::fake();

        // Test single resource change
        ResourceChangedDispatcher::dispatchSingle(
            $this->namespace,
            $this->cluster,
            'deployment',
            'created',
            $this->sampleDeployment
        );

        Event::assertDispatched(ResourceChanged::class, function ($event) {
            return isset($event->changes['created'])
                && count($event->changes['created']) === 1;
        });

        Event::assertDispatched(DeploymentCreated::class);
    }

    public function test_multiple_resource_changes_in_batch()
    {
        Event::fake();

        $deployment2 = $this->sampleDeployment;
        $deployment2['metadata']['name'] = 'test-deployment-2';

        $deployment3 = $this->sampleDeployment;
        $deployment3['metadata']['name'] = 'test-deployment-3';

        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->cluster,
            'deployment',
            [
                'created' => [$this->sampleDeployment, $deployment2],
                'deleted' => [$deployment3],
            ]
        );

        // Should dispatch one ResourceChanged event
        Event::assertDispatched(ResourceChanged::class, function ($event) {
            return count($event->changes['created']) === 2
                && count($event->changes['deleted']) === 1
                && $event->summary['total_changes'] === 3;
        });

        // Should dispatch specific events for each resource
        Event::assertDispatchedTimes(DeploymentCreated::class, 2);
        Event::assertDispatchedTimes(DeploymentDeleted::class, 1);
    }

    public function test_unsupported_resource_type_fails_gracefully()
    {
        Event::fake();

        ResourceChangedDispatcher::dispatch(
            $this->namespace,
            $this->cluster,
            'unsupported-resource',
            [
                'created' => [$this->sampleDeployment],
            ]
        );

        // ResourceChanged should still be dispatched
        Event::assertDispatched(ResourceChanged::class);

        // But no specific K8s events should be dispatched
        Event::assertNotDispatched(DeploymentCreated::class);
    }

    public function test_broadcast_channels_are_correct()
    {
        $event = new DeploymentCreated(
            $this->namespace,
            $this->cluster->name,
            $this->cluster->id,
            'test-deployment',
            $this->sampleDeployment
        );

        $channels = $event->broadcastOn();

        $this->assertCount(2, $channels);
        $this->assertEquals("workspace.{$this->namespace}.deployment", $channels[0]->name);
        $this->assertEquals("workspace.{$this->namespace}.resources", $channels[1]->name);
    }

    public function test_broadcast_event_name_is_correct()
    {
        $event = new DeploymentCreated(
            $this->namespace,
            $this->cluster->name,
            $this->cluster->id,
            'test-deployment',
            $this->sampleDeployment
        );

        $this->assertEquals('deployment.created', $event->broadcastAs());
    }

    public function test_broadcast_data_contains_expected_fields()
    {
        $event = new DeploymentCreated(
            $this->namespace,
            $this->cluster->name,
            $this->cluster->id,
            'test-deployment',
            $this->sampleDeployment
        );

        $broadcastData = $event->broadcastWith();

        $this->assertEquals($this->namespace, $broadcastData['namespace']);
        $this->assertEquals($this->cluster->id, $broadcastData['cluster']['id']);
        $this->assertEquals($this->cluster->name, $broadcastData['cluster']['name']);
        $this->assertEquals('deployment', $broadcastData['resource_type']);
        $this->assertEquals('test-deployment', $broadcastData['resource_name']);
        $this->assertEquals('created', $broadcastData['action']);
        $this->assertArrayHasKey('resource', $broadcastData);
        $this->assertArrayHasKey('timestamp', $broadcastData);
    }
}
