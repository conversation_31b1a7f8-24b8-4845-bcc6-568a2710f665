<?php

namespace Tests\Feature;

use App\Models\Cluster;
use App\Models\User;
use App\Models\Workspace;
use App\Service\WorkspaceRbacService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WorkspaceRbacTest extends TestCase
{
    use RefreshDatabase;

    private Cluster $cluster;

    private User $user;

    private WorkspaceRbacService $rbacService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->cluster = Cluster::factory()->create();
        $this->user = User::factory()->create();
        $this->rbacService = new WorkspaceRbacService;
    }

    public function test_rbac_configuration_exists()
    {
        // 验证 RBAC 配置是否正确设置
        $this->assertTrue(config('k8s.rbac.enabled'));

        $serviceAccountConfig = config('k8s.rbac.serviceAccount');
        $this->assertIsArray($serviceAccountConfig);
        $this->assertArrayHasKey('name', $serviceAccountConfig);
        $this->assertEquals('workspace-user', $serviceAccountConfig['name']);

        $roleConfig = config('k8s.rbac.role');
        $this->assertIsArray($roleConfig);
        $this->assertArrayHasKey('name', $roleConfig);
        $this->assertArrayHasKey('rules', $roleConfig);
        $this->assertEquals('workspace-role', $roleConfig['name']);
        $this->assertNotEmpty($roleConfig['rules']);

        $roleBindingConfig = config('k8s.rbac.roleBinding');
        $this->assertIsArray($roleBindingConfig);
        $this->assertArrayHasKey('name', $roleBindingConfig);
        $this->assertEquals('workspace-rolebinding', $roleBindingConfig['name']);
    }

    public function test_workspace_creation_includes_rbac_setup()
    {
        // 创建工作空间
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-rbac-workspace',
            'namespace' => 'ns-rbac-test',
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 验证工作空间具有构建默认标签的能力
        $labels = $workspace->buildDefaultLabels($workspace);
        $this->assertIsArray($labels);
        $this->assertArrayHasKey('workspace.paas.platform/name', $labels);
    }

    public function test_rbac_service_check_workspace_rbac()
    {
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-check-workspace',
            'namespace' => 'ns-check-test',
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 检查 RBAC 状态（由于没有真实的 K8s 集群，这会返回 false）
        $status = $this->rbacService->checkWorkspaceRbac($workspace);

        $this->assertIsArray($status);
        $this->assertArrayHasKey('service_account', $status);
        $this->assertArrayHasKey('role', $status);
        $this->assertArrayHasKey('role_binding', $status);
    }

    public function test_rbac_permissions_configuration()
    {
        $roleConfig = config('k8s.rbac.role');
        $rules = $roleConfig['rules'];

        // 验证包含基本的 Pod 权限
        $podRule = collect($rules)->first(function ($rule) {
            return in_array('pods', $rule['resources']) && in_array('', $rule['apiGroups']);
        });
        $this->assertNotNull($podRule, 'Pod 权限规则应该存在');
        $this->assertContains('get', $podRule['verbs']);
        $this->assertContains('list', $podRule['verbs']);
        $this->assertContains('create', $podRule['verbs']);

        // 验证包含 Deployment 权限
        $deploymentRule = collect($rules)->first(function ($rule) {
            return in_array('deployments', $rule['resources']) && in_array('apps', $rule['apiGroups']);
        });
        $this->assertNotNull($deploymentRule, 'Deployment 权限规则应该存在');

        // 验证包含 Service 权限
        $serviceRule = collect($rules)->first(function ($rule) {
            return in_array('services', $rule['resources']) && in_array('', $rule['apiGroups']);
        });
        $this->assertNotNull($serviceRule, 'Service 权限规则应该存在');

        // 验证包含 ConfigMap 权限
        $configMapRule = collect($rules)->first(function ($rule) {
            return in_array('configmaps', $rule['resources']) && in_array('', $rule['apiGroups']);
        });
        $this->assertNotNull($configMapRule, 'ConfigMap 权限规则应该存在');

        // 验证包含 Secret 权限
        $secretRule = collect($rules)->first(function ($rule) {
            return in_array('secrets', $rule['resources']) && in_array('', $rule['apiGroups']);
        });
        $this->assertNotNull($secretRule, 'Secret 权限规则应该存在');

        // 验证 RBAC 资源只有只读权限（安全检查）
        $rbacRule = collect($rules)->first(function ($rule) {
            return in_array('roles', $rule['resources']) && in_array('rbac.authorization.k8s.io', $rule['apiGroups']);
        });
        $this->assertNotNull($rbacRule, 'RBAC 权限规则应该存在');
        $this->assertContains('get', $rbacRule['verbs'], 'RBAC 应该有读取权限');
        $this->assertContains('list', $rbacRule['verbs'], 'RBAC 应该有列表权限');
        $this->assertNotContains('delete', $rbacRule['verbs'], 'RBAC 不应该有删除权限');
        $this->assertNotContains('update', $rbacRule['verbs'], 'RBAC 不应该有更新权限');
        $this->assertNotContains('patch', $rbacRule['verbs'], 'RBAC 不应该有修改权限');

        // 验证 Service Account 只有只读权限（安全检查）
        $saRule = collect($rules)->first(function ($rule) {
            return in_array('serviceaccounts', $rule['resources']) && in_array('', $rule['apiGroups']);
        });
        $this->assertNotNull($saRule, 'Service Account 权限规则应该存在');
        $this->assertContains('get', $saRule['verbs'], 'Service Account 应该有读取权限');
        $this->assertContains('list', $saRule['verbs'], 'Service Account 应该有列表权限');
        $this->assertNotContains('delete', $saRule['verbs'], 'Service Account 不应该有删除权限');
        $this->assertNotContains('update', $saRule['verbs'], 'Service Account 不应该有更新权限');
        $this->assertNotContains('patch', $saRule['verbs'], 'Service Account 不应该有修改权限');
    }

    public function test_rbac_service_creates_proper_resource_structure()
    {
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-structure-workspace',
            'namespace' => 'ns-structure-test',
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 由于没有真实的 K8s 集群，我们不能测试实际的 API 调用
        // 但我们可以验证服务类存在且方法可调用
        $this->assertTrue(method_exists($this->rbacService, 'createWorkspaceRbac'));
        $this->assertTrue(method_exists($this->rbacService, 'deleteWorkspaceRbac'));
        $this->assertTrue(method_exists($this->rbacService, 'checkWorkspaceRbac'));
        $this->assertTrue(method_exists($this->rbacService, 'syncWorkspaceRbac'));
    }

    public function test_workspace_deletion_includes_rbac_cleanup()
    {
        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-deletion-workspace',
            'namespace' => 'ns-deletion-test',
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 验证删除方法存在
        $this->assertTrue(method_exists($this->rbacService, 'deleteWorkspaceRbac'));
    }

    public function test_rbac_service_handles_disabled_rbac()
    {
        // 临时禁用 RBAC
        config(['k8s.rbac.enabled' => false]);

        $workspace = Workspace::factory()->create([
            'user_id' => $this->user->id,
            'cluster_id' => $this->cluster->id,
            'name' => 'test-disabled-workspace',
            'namespace' => 'ns-disabled-test',
            'status' => Workspace::STATUS_ACTIVE,
        ]);

        // 当 RBAC 禁用时，创建应该返回 true 但不执行任何操作
        $result = $this->rbacService->createWorkspaceRbac($workspace);
        $this->assertTrue($result);

        // 恢复 RBAC 启用状态
        config(['k8s.rbac.enabled' => true]);
    }

    public function test_rbac_security_restrictions()
    {
        $roleConfig = config('k8s.rbac.role');
        $rules = $roleConfig['rules'];

        // 检查 RBAC 相关资源的权限限制
        $rbacRule = collect($rules)->first(function ($rule) {
            return in_array('rbac.authorization.k8s.io', $rule['apiGroups']) &&
                   in_array('roles', $rule['resources']);
        });

        $this->assertNotNull($rbacRule, 'RBAC 权限规则必须存在');

        // 验证 RBAC 资源只有安全的操作权限
        $allowedVerbs = ['get', 'list', 'watch'];
        $dangerousVerbs = ['create', 'update', 'patch', 'delete'];

        foreach ($allowedVerbs as $verb) {
            $this->assertContains($verb, $rbacRule['verbs'], "RBAC 规则应该包含 {$verb} 权限");
        }

        foreach ($dangerousVerbs as $verb) {
            $this->assertNotContains($verb, $rbacRule['verbs'], "RBAC 规则不应该包含 {$verb} 权限，以防止用户删除自己的权限");
        }

        // 检查 Service Account 的权限限制
        $saRule = collect($rules)->first(function ($rule) {
            return in_array('', $rule['apiGroups']) &&
                   in_array('serviceaccounts', $rule['resources']);
        });

        $this->assertNotNull($saRule, 'Service Account 权限规则必须存在');

        foreach ($allowedVerbs as $verb) {
            $this->assertContains($verb, $saRule['verbs'], "Service Account 规则应该包含 {$verb} 权限");
        }

        foreach ($dangerousVerbs as $verb) {
            $this->assertNotContains($verb, $saRule['verbs'], "Service Account 规则不应该包含 {$verb} 权限，以防止用户删除自己的身份");
        }
    }

    public function test_role_uses_namespaced_permissions_only()
    {
        // 验证使用的是 Role 而不是 ClusterRole（通过配置结构确认）
        $roleConfig = config('k8s.rbac.role');

        // Role 配置应该存在且命名为 workspace-role
        $this->assertEquals('workspace-role', $roleConfig['name']);

        // 验证没有集群级别的权限配置
        $this->assertArrayNotHasKey('clusterRole', config('k8s.rbac'));
        $this->assertArrayNotHasKey('clusterRoleBinding', config('k8s.rbac'));

        // 所有权限都应该是命名空间级别的资源
        $rules = $roleConfig['rules'];
        foreach ($rules as $rule) {
            // 检查没有集群级别的资源
            $clusterResources = ['nodes', 'namespaces', 'clusterroles', 'clusterrolebindings', 'persistentvolumes'];
            foreach ($clusterResources as $clusterResource) {
                $this->assertNotContains($clusterResource, $rule['resources'],
                    "不应该包含集群级别的资源: {$clusterResource}");
            }
        }
    }
}
