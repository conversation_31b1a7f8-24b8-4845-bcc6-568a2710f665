<?php

namespace Tests\Feature;

use App\Listeners\WebhookCallEventListener;
use App\Models\WebhookDelivery;
use App\Models\WebhookEndpoint;
use App\Models\Workspace;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Spatie\WebhookServer\Events\FinalWebhookCallFailedEvent;
use Spatie\WebhookServer\Events\WebhookCallFailedEvent;
use Spatie\WebhookServer\Events\WebhookCallSucceededEvent;
use Spatie\WebhookServer\WebhookCall;
use Tests\TestCase;

class SpatieWebhookServerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // 创建测试数据
        $this->workspace = Workspace::factory()->create();
        $this->endpoint = WebhookEndpoint::factory()->create([
            'workspace_id' => $this->workspace->id,
            'url' => 'https://example.com/webhook',
            'events' => ['test.event'],
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_send_webhook_using_spatie_package()
    {
        Http::fake([
            'example.com/*' => Http::response(['status' => 'ok'], 200),
        ]);

        Event::fake();

        $payload = ['test' => 'data', 'timestamp' => now()->toISOString()];

        WebhookCall::create()
            ->url($this->endpoint->url)
            ->payload($payload)
            ->useSecret($this->endpoint->secret)
            ->meta([
                'delivery_id' => 123,
                'endpoint_id' => $this->endpoint->id,
                'event_type' => 'test.event',
            ])
            ->dispatchSync();

        // 验证事件被触发
        Event::assertDispatched(WebhookCallSucceededEvent::class);
    }

    /** @test */
    public function webhook_call_event_listener_handles_success_event()
    {
        $listener = new WebhookCallEventListener;

        // 创建一个 delivery 记录
        $delivery = WebhookDelivery::create([
            'webhook_endpoint_id' => $this->endpoint->id,
            'url' => $this->endpoint->url,
            'event_type' => 'test.event',
            'payload' => ['test' => 'data'],
            'status' => 'pending',
        ]);

        // 模拟成功事件
        $event = new WebhookCallSucceededEvent(
            httpVerb: 'post',
            webhookUrl: $this->endpoint->url,
            payload: ['test' => 'data'],
            headers: ['Content-Type' => 'application/json'],
            meta: [
                'delivery_id' => $delivery->id,
                'endpoint_id' => $this->endpoint->id,
                'event_type' => 'test.event',
            ],
            tags: [],
            uuid: 'test-uuid',
            attempt: 1,
            response: new \GuzzleHttp\Psr7\Response(200, [], '{"status":"ok"}')
        );

        $listener->handleWebhookCallSucceeded($event);

        // 验证 delivery 状态被更新
        $delivery->refresh();
        $this->assertEquals('success', $delivery->status);
        $this->assertEquals(1, $delivery->attempts);
        $this->assertEquals(200, $delivery->response_status);
    }

    /** @test */
    public function webhook_call_event_listener_handles_failed_event()
    {
        $listener = new WebhookCallEventListener;

        // 创建一个 delivery 记录
        $delivery = WebhookDelivery::create([
            'webhook_endpoint_id' => $this->endpoint->id,
            'url' => $this->endpoint->url,
            'event_type' => 'test.event',
            'payload' => ['test' => 'data'],
            'status' => 'pending',
        ]);

        // 模拟失败事件
        $event = new WebhookCallFailedEvent(
            httpVerb: 'post',
            webhookUrl: $this->endpoint->url,
            payload: ['test' => 'data'],
            headers: ['Content-Type' => 'application/json'],
            meta: [
                'delivery_id' => $delivery->id,
                'endpoint_id' => $this->endpoint->id,
                'event_type' => 'test.event',
            ],
            tags: [],
            uuid: 'test-uuid',
            attempt: 1,
            response: new \GuzzleHttp\Psr7\Response(500, [], '{"error":"Internal Server Error"}')
        );

        $listener->handleWebhookCallFailed($event);

        // 验证 delivery 状态被更新
        $delivery->refresh();
        $this->assertEquals('failed', $delivery->status);
        $this->assertEquals(1, $delivery->attempts);
        $this->assertEquals(500, $delivery->response_status);
    }

    /** @test */
    public function webhook_call_event_listener_handles_final_failed_event()
    {
        $listener = new WebhookCallEventListener;

        // 创建一个 delivery 记录
        $delivery = WebhookDelivery::create([
            'webhook_endpoint_id' => $this->endpoint->id,
            'url' => $this->endpoint->url,
            'event_type' => 'test.event',
            'payload' => ['test' => 'data'],
            'status' => 'pending',
        ]);

        // 模拟最终失败事件
        $event = new FinalWebhookCallFailedEvent(
            httpVerb: 'post',
            webhookUrl: $this->endpoint->url,
            payload: ['test' => 'data'],
            headers: ['Content-Type' => 'application/json'],
            meta: [
                'delivery_id' => $delivery->id,
                'endpoint_id' => $this->endpoint->id,
                'event_type' => 'test.event',
            ],
            tags: [],
            uuid: 'test-uuid',
            attempt: 3,
            response: new \GuzzleHttp\Psr7\Response(500, [], '{"error":"Internal Server Error"}')
        );

        $listener->handleFinalWebhookCallFailed($event);

        // 验证 delivery 状态被更新
        $delivery->refresh();
        $this->assertEquals('failed', $delivery->status);
        $this->assertEquals(3, $delivery->attempts);
        $this->assertEquals(500, $delivery->response_status);
    }
}
