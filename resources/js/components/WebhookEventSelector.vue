<script setup lang="ts">
import { AVAILABLE_WEBHOOK_EVENTS } from '@/constants/webhook-events';
import { computed } from 'vue';

interface Props {
    selectedEvents: string[];
    errors?: string;
}

interface Emits {
    (e: 'update:selectedEvents', events: string[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const toggleEvent = (eventValue: string) => {
    const currentEvents = [...props.selectedEvents];
    const index = currentEvents.indexOf(eventValue);

    if (index > -1) {
        currentEvents.splice(index, 1);
    } else {
        // 如果选择了全部事件，清除其他选项
        if (eventValue === '*') {
            emit('update:selectedEvents', ['*']);
            return;
        } else {
            // 如果选择了具体事件，移除全部事件选项
            const allIndex = currentEvents.indexOf('*');
            if (allIndex > -1) {
                currentEvents.splice(allIndex, 1);
            }
            currentEvents.push(eventValue);
        }
    }

    emit('update:selectedEvents', currentEvents);
};

const isEventSelected = (eventValue: string) => {
    return props.selectedEvents.includes(eventValue);
};

const selectedEventLabels = computed(() => {
    return props.selectedEvents.map((event) => AVAILABLE_WEBHOOK_EVENTS.find((e) => e.value === event)?.label || event);
});
</script>

<template>
    <div class="space-y-4">
        <!-- 提示信息 -->
        <div class="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-950/50">
            <div class="flex items-start gap-2">
                <svg class="mt-0.5 h-4 w-4 flex-shrink-0 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                </svg>
                <p class="text-sm text-blue-800 dark:text-blue-200">订阅"全部事件"会接收大量通知，可能影响性能。建议根据实际需要选择具体事件类型。</p>
            </div>
        </div>

        <!-- 事件选择列表 -->
        <div class="space-y-3">
            <div
                v-for="event in AVAILABLE_WEBHOOK_EVENTS"
                :key="event.value"
                class="flex cursor-pointer items-start space-x-3 rounded-lg border p-3 transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/50"
                :class="{ 'bg-gray-50 dark:bg-gray-800/50': isEventSelected(event.value) }"
                @click="toggleEvent(event.value)"
            >
                <!-- 原生 HTML checkbox -->
                <input
                    type="checkbox"
                    :checked="isEventSelected(event.value)"
                    @click.stop="toggleEvent(event.value)"
                    class="mt-1 h-4 w-4 rounded border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-blue-600"
                />

                <div class="flex-1 space-y-1">
                    <div class="flex items-center gap-2">
                        <label class="cursor-pointer font-medium text-gray-900 dark:text-gray-100">
                            {{ event.label }}
                        </label>
                        <span
                            v-if="event.value === '*'"
                            class="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                        >
                            性能影响
                        </span>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        {{ event.description }}
                    </p>
                </div>
            </div>
        </div>

        <!-- 错误信息 -->
        <p v-if="errors" class="text-sm text-red-600 dark:text-red-400">
            {{ errors }}
        </p>

        <!-- 已选择事件显示 -->
        <div v-if="selectedEvents.length > 0" class="border-t border-gray-200 pt-4 dark:border-gray-700">
            <label class="text-sm font-medium text-gray-900 dark:text-gray-100">已选择事件：</label>
            <div class="mt-2 flex flex-wrap gap-2">
                <span
                    v-for="label in selectedEventLabels"
                    :key="label"
                    class="inline-flex items-center rounded-full border border-blue-200 bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:border-blue-700 dark:bg-blue-900 dark:text-blue-200"
                >
                    {{ label }}
                </span>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* 确保 checkbox 样式在所有浏览器中一致 */
input[type='checkbox'] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

input[type='checkbox']:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

input[type='checkbox']:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.dark input[type='checkbox']:checked {
    background-color: #2563eb;
    border-color: #2563eb;
}
</style>
