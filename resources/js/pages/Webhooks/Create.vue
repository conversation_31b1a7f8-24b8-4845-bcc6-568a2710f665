<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import WebhookEventSelector from '@/components/WebhookEventSelector.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import type { BreadcrumbItemType } from '@/types';
import type { WebhookFormData } from '@/types/webhook';
import { Head, Link, useForm } from '@inertiajs/vue3';
import { ArrowLeft, Plus, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { ref } from 'vue';

const breadcrumbs: BreadcrumbItemType[] = [
    {
        title: 'Webhook 管理',
        href: '/webhooks',
    },
    {
        title: '创建 Webhook',
        href: '/webhooks/create',
    },
];

const form = useForm<WebhookFormData>({
    name: '',
    url: '',
    events: [],
    headers: {},
    is_active: true,
    timeout: 30,
    max_attempts: 3,
});

const newHeaderKey = ref('');
const newHeaderValue = ref('');

const addHeader = () => {
    if (newHeaderKey.value && newHeaderValue.value) {
        form.headers[newHeaderKey.value] = newHeaderValue.value;
        newHeaderKey.value = '';
        newHeaderValue.value = '';
    }
};

const removeHeader = (key: string) => {
    delete form.headers[key];
};

const submit = () => {
    form.post(route('webhooks.store'), {
        onSuccess: () => {
            toast.success('创建成功', {
                description: '已成功创建 Webhook 端点',
            });
        },
        onError: (errors) => {
            toast.error('创建失败', {
                description: Object.values(errors)[0] as string,
            });
        },
    });
};
</script>

<template>
    <Head title="创建 Webhook" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="space-y-6 p-4">
            <!-- 头部 -->
            <div class="flex items-center gap-4">
                <Button variant="ghost" size="sm" as-child>
                    <Link :href="route('webhooks.index')">
                        <ArrowLeft class="h-4 w-4" />
                    </Link>
                </Button>

                <div>
                    <h1 class="text-3xl font-bold">创建 Webhook</h1>
                    <p class="mt-2 text-muted-foreground">配置新的 Webhook 端点来接收平台事件通知</p>
                </div>
            </div>

            <form @submit.prevent="submit" class="space-y-6">
                <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    <!-- 基础配置 -->
                    <div class="space-y-6 lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>基础信息</CardTitle>
                                <CardDescription> 配置 Webhook 的基本信息和目标 URL </CardDescription>
                            </CardHeader>
                            <CardContent class="space-y-4">
                                <div class="space-y-2">
                                    <Label for="name">名称</Label>
                                    <Input id="name" v-model="form.name" placeholder="例如：生产环境通知" />
                                    <p v-if="form.errors && 'name' in form.errors" class="text-sm text-destructive">
                                        {{ (form.errors as any).name }}
                                    </p>
                                </div>

                                <div class="space-y-2">
                                    <Label for="url">Webhook URL</Label>
                                    <Input id="url" v-model="form.url" type="url" placeholder="https://your-app.com/webhooks/endpoint" />
                                    <p v-if="form.errors && 'url' in form.errors" class="text-sm text-destructive">
                                        {{ (form.errors as any).url }}
                                    </p>
                                    <p class="text-sm text-muted-foreground">当事件发生时，我们将向此 URL 发送 HTTP POST 请求</p>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <Switch id="is_active" :model-value="form.is_active" @update:model-value="(val) => (form.is_active = val)" />
                                    <Label for="is_active">启用此 Webhook</Label>
                                </div>
                            </CardContent>
                        </Card>

                        <!-- 事件订阅 -->
                        <Card>
                            <CardHeader>
                                <CardTitle>事件订阅</CardTitle>
                                <CardDescription> 选择要接收的事件类型。选择"全部事件"会影响性能，建议只订阅需要的事件。 </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <WebhookEventSelector
                                    v-model:selected-events="form.events"
                                    :errors="form.errors && 'events' in form.errors ? (form.errors as any).events : undefined"
                                />
                            </CardContent>
                        </Card>

                        <!-- 自定义请求头 -->
                        <Card>
                            <CardHeader>
                                <CardTitle>自定义请求头</CardTitle>
                                <CardDescription> 为 Webhook 请求添加自定义 HTTP 头部 </CardDescription>
                            </CardHeader>
                            <CardContent class="space-y-4">
                                <!-- 添加新头部 -->
                                <div class="flex gap-2">
                                    <Input v-model="newHeaderKey" placeholder="Header 名称" class="flex-1" />
                                    <Input v-model="newHeaderValue" placeholder="Header 值" class="flex-1" />
                                    <Button type="button" size="sm" @click="addHeader" :disabled="!newHeaderKey || !newHeaderValue">
                                        <Plus class="h-4 w-4" />
                                    </Button>
                                </div>

                                <!-- 已添加的头部列表 -->
                                <div v-if="Object.keys(form.headers).length > 0" class="space-y-2">
                                    <Separator />
                                    <div v-for="(value, key) in form.headers" :key="key" class="flex items-center justify-between rounded border p-2">
                                        <div class="flex-1">
                                            <span class="font-mono text-sm font-medium">{{ key }}:</span>
                                            <span class="ml-2 font-mono text-sm">{{ value }}</span>
                                        </div>
                                        <Button type="button" variant="ghost" size="sm" @click="removeHeader(key)">
                                            <Trash2 class="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>

                                <p v-if="form.errors && 'headers' in form.errors" class="text-sm text-destructive">
                                    {{ (form.errors as any).headers }}
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    <!-- 侧边栏信息 -->
                    <div class="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle>Webhook 说明</CardTitle>
                            </CardHeader>
                            <CardContent class="space-y-4 text-sm">
                                <div>
                                    <h4 class="mb-2 font-medium">安全签名</h4>
                                    <p class="text-muted-foreground">每个 Webhook 请求都会包含签名头部，用于验证请求来源的真实性。</p>
                                </div>

                                <Separator />

                                <div>
                                    <h4 class="mb-2 font-medium">重试机制</h4>
                                    <p class="text-muted-foreground">如果请求失败，系统会自动重试，重试次数和超时时间由系统配置决定。</p>
                                </div>

                                <Separator />

                                <div>
                                    <h4 class="mb-2 font-medium">事件格式</h4>
                                    <p class="text-muted-foreground">所有事件都会以 JSON 格式发送，包含事件类型、时间戳和相关数据。</p>
                                </div>
                            </CardContent>
                        </Card>

                        <!-- 操作按钮 -->
                        <div class="space-y-3">
                            <Button type="submit" class="w-full" :disabled="form.processing">
                                {{ form.processing ? '创建中...' : '创建 Webhook' }}
                            </Button>

                            <Button variant="outline" class="w-full" as-child>
                                <Link :href="route('webhooks.index')"> 取消 </Link>
                            </Button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </AppLayout>
</template>
