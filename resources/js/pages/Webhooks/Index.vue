<script setup lang="ts">
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/AppLayout.vue';
import { formatDate } from '@/lib/formatDate';
import type { BreadcrumbItemType } from '@/types';
import type { WebhookEndpoint } from '@/types/webhook';
import { Head, Link, router } from '@inertiajs/vue3';
import { Eye, Key, MoreHorizontal, Plus, Settings, TestTube, ToggleLeft, ToggleRight, Trash2 } from 'lucide-vue-next';
import { toast } from 'sonner';
import { ref } from 'vue';

interface Props {
    webhooks: WebhookEndpoint[];
}

const props = defineProps<Props>();

const breadcrumbs: BreadcrumbItemType[] = [
    {
        title: 'Webhook 管理',
        href: '/webhooks',
    },
];

const showDeleteDialog = ref(false);
const webhookToDelete = ref<WebhookEndpoint | null>(null);

// formatDate 已通过工具函数引入，直接用于模板

const getStatusBadge = (webhook: WebhookEndpoint) => {
    if (!webhook.is_active) {
        return { variant: 'secondary' as const, text: '已禁用' };
    }

    if (webhook.success_rate !== undefined) {
        if (webhook.success_rate >= 95) {
            return { variant: 'default' as const, text: '健康' };
        } else if (webhook.success_rate >= 80) {
            return { variant: 'secondary' as const, text: '警告' };
        } else {
            return { variant: 'destructive' as const, text: '异常' };
        }
    }

    return { variant: 'default' as const, text: '活跃' };
};

const testWebhook = async (webhook: WebhookEndpoint) => {
    if (!webhook.id) {
        toast.error('错误', { description: 'Webhook ID 不存在' });
        return;
    }

    try {
        const response = await router.post(
            `/webhooks/${webhook.id}/test`,
            {},
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: (page) => {
                    const result = page.props.flash?.test_result;
                    if (result?.success) {
                        toast.success('测试成功', {
                            description: result.message || '已发送测试Webhook',
                        });
                    } else {
                        toast.error('测试失败', {
                            description: result?.error_message || '未知错误',
                        });
                    }
                },
                onError: (errors) => {
                    toast.error('测试失败', {
                        description: Object.values(errors)[0] as string,
                    });
                },
            },
        );
    } catch (error) {
        toast.error('测试失败', {
            description: '请求发送失败',
        });
    }
};

const toggleWebhook = async (webhook: WebhookEndpoint) => {
    if (!webhook.id) {
        toast.error('错误', { description: 'Webhook ID 不存在' });
        return;
    }

    try {
        await router.post(
            `/webhooks/${webhook.id}/toggle`,
            {},
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    const action = webhook.is_active ? '禁用' : '启用';
                    toast.success(`${action}成功`, {
                        description: `已${action} ${webhook.name}`,
                    });
                },
                onError: (errors) => {
                    toast.error('操作失败', {
                        description: Object.values(errors)[0] as string,
                    });
                },
            },
        );
    } catch (error) {
        toast.error('操作失败', {
            description: '请求发送失败',
        });
    }
};

const regenerateSecret = async (webhook: WebhookEndpoint) => {
    if (!webhook.id) {
        toast.error('错误', { description: 'Webhook ID 不存在' });
        return;
    }

    try {
        await router.post(
            `/webhooks/${webhook.id}/regenerate-secret`,
            {},
            {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    toast.success('密钥重新生成成功', {
                        description: `已为 ${webhook.name} 生成新的签名密钥`,
                    });
                },
                onError: (errors) => {
                    toast.error('操作失败', {
                        description: Object.values(errors)[0] as string,
                    });
                },
            },
        );
    } catch (error) {
        toast.error('操作失败', {
            description: '请求发送失败',
        });
    }
};

const confirmDelete = (webhook: WebhookEndpoint) => {
    webhookToDelete.value = webhook;
    showDeleteDialog.value = true;
};

const deleteWebhook = async () => {
    if (!webhookToDelete.value) return;

    try {
        await router.delete(`/webhooks/${webhookToDelete.value.id}`, {
            preserveState: true,
            preserveScroll: true,
            onSuccess: () => {
                toast.success('删除成功', {
                    description: `已删除 ${webhookToDelete.value?.name}`,
                });
                showDeleteDialog.value = false;
                webhookToDelete.value = null;
            },
            onError: (errors) => {
                toast.error('删除失败', {
                    description: Object.values(errors)[0] as string,
                });
            },
        });
    } catch (error) {
        toast.error('删除失败', {
            description: '请求发送失败',
        });
    }
};
</script>

<template>
    <Head title="Webhook 管理" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="max-h-screen space-y-6 overflow-y-auto p-4">
            <!-- 头部 -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">Webhook 管理</h1>
                    <p class="mt-2 text-muted-foreground">管理您的Webhook端点，接收平台事件通知</p>
                </div>

                <Button as-child>
                    <Link :href="route('webhooks.create')">
                        <Plus class="mr-2 h-4 w-4" />
                        创建 Webhook
                    </Link>
                </Button>
            </div>

            <!-- Webhook列表 -->
            <div class="overflow-hidden rounded-lg border">
                <div class="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead class="min-w-[120px]">名称</TableHead>
                                <TableHead class="min-w-[180px]">URL</TableHead>
                                <TableHead class="min-w-[140px]">事件</TableHead>
                                <TableHead class="min-w-[60px]">状态</TableHead>
                                <TableHead class="min-w-[60px]">成功率</TableHead>
                                <TableHead class="min-w-[100px]">最后发送</TableHead>
                                <TableHead class="w-10"></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            <TableRow v-if="props.webhooks.length === 0">
                                <TableCell colspan="7" class="py-8 text-center text-muted-foreground">
                                    暂无Webhook端点，
                                    <Link :href="route('webhooks.create')" class="text-primary hover:underline"> 创建第一个 </Link>
                                </TableCell>
                            </TableRow>

                            <TableRow v-for="webhook in props.webhooks" :key="webhook.id">
                                <TableCell class="font-medium">
                                    <div class="max-w-[120px]">
                                        <Link
                                            v-if="webhook.id"
                                            :href="route('webhooks.show', webhook.id)"
                                            class="block truncate hover:underline"
                                            :title="webhook.name"
                                        >
                                            {{ webhook.name }}
                                        </Link>
                                        <span v-else class="block truncate" :title="webhook.name">{{ webhook.name }}</span>
                                    </div>
                                </TableCell>

                                <TableCell class="font-mono text-sm">
                                    <div class="max-w-[180px]">
                                        <span class="block truncate" :title="webhook.url">
                                            {{ webhook.url }}
                                        </span>
                                    </div>
                                </TableCell>

                                <TableCell>
                                    <div class="max-w-[140px]">
                                        <div class="flex flex-wrap gap-1">
                                            <Badge
                                                v-for="event in (webhook.events || []).slice(0, 1)"
                                                :key="event"
                                                variant="outline"
                                                class="text-xs whitespace-nowrap"
                                            >
                                                {{ event === '*' ? '全部' : event.split('.')[0] }}
                                            </Badge>
                                            <Badge v-if="(webhook.events || []).length > 1" variant="outline" class="text-xs whitespace-nowrap">
                                                +{{ (webhook.events || []).length - 1 }}
                                            </Badge>
                                        </div>
                                    </div>
                                </TableCell>

                                <TableCell>
                                    <Badge :variant="getStatusBadge(webhook).variant">
                                        {{ getStatusBadge(webhook).text }}
                                    </Badge>
                                </TableCell>

                                <TableCell>
                                    <span v-if="webhook.success_rate !== undefined"> {{ webhook.success_rate }}% </span>
                                    <span v-else class="text-muted-foreground">-</span>
                                </TableCell>

                                <TableCell class="text-sm text-muted-foreground">
                                    <div
                                        class="max-w-[100px] truncate"
                                        :title="webhook.last_delivered_at ? new Date(webhook.last_delivered_at).toLocaleString('zh-CN') : '从未发送'"
                                    >
                                        {{ formatDate(webhook.last_delivered_at) }}
                                    </div>
                                </TableCell>

                                <TableCell>
                                    <DropdownMenu>
                                        <DropdownMenuTrigger as-child>
                                            <Button variant="ghost" size="sm">
                                                <MoreHorizontal class="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem as-child v-if="webhook.id">
                                                <Link :href="route('webhooks.show', webhook.id)">
                                                    <Eye class="mr-2 h-4 w-4" />
                                                    查看详情
                                                </Link>
                                            </DropdownMenuItem>

                                            <DropdownMenuItem as-child v-if="webhook.id">
                                                <Link :href="route('webhooks.edit', webhook.id)">
                                                    <Settings class="mr-2 h-4 w-4" />
                                                    编辑设置
                                                </Link>
                                            </DropdownMenuItem>

                                            <DropdownMenuSeparator />

                                            <DropdownMenuItem @click="testWebhook(webhook)">
                                                <TestTube class="mr-2 h-4 w-4" />
                                                发送测试
                                            </DropdownMenuItem>

                                            <DropdownMenuItem @click="toggleWebhook(webhook)">
                                                <component :is="webhook.is_active ? ToggleLeft : ToggleRight" class="mr-2 h-4 w-4" />
                                                {{ webhook.is_active ? '禁用' : '启用' }}
                                            </DropdownMenuItem>

                                            <DropdownMenuItem @click="regenerateSecret(webhook)">
                                                <Key class="mr-2 h-4 w-4" />
                                                重新生成密钥
                                            </DropdownMenuItem>

                                            <DropdownMenuSeparator />

                                            <DropdownMenuItem @click="confirmDelete(webhook)" class="text-destructive focus:text-destructive">
                                                <Trash2 class="mr-2 h-4 w-4" />
                                                删除
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </div>
            </div>

            <!-- 删除确认对话框 -->
            <AlertDialog v-model:open="showDeleteDialog">
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>确认删除</AlertDialogTitle>
                        <AlertDialogDescription>
                            您确定要删除 "{{ webhookToDelete?.name }}" 吗？ 此操作无法撤销，所有相关的发送记录也将被删除。
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>取消</AlertDialogCancel>
                        <AlertDialogAction @click="deleteWebhook" class="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                            删除
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </div>
    </AppLayout>
</template>
