export interface WebhookEvent {
    value: string;
    label: string;
    description: string;
}

export const AVAILABLE_WEBHOOK_EVENTS: WebhookEvent[] = [
    { value: '*', label: '全部事件', description: '接收所有平台事件（性能影响较大）' },
    { value: 'deployment.created', label: 'Deployment 创建', description: '当创建新的 Deployment 时触发' },
    { value: 'deployment.updated', label: 'Deployment 更新', description: '当 Deployment 配置更新时触发' },
    { value: 'deployment.deleted', label: 'Deployment 删除', description: '当删除 Deployment 时触发' },
    { value: 'deployment.scaled', label: 'Deployment 扩缩容', description: '当 Deployment 副本数变化时触发' },
    { value: 'statefulset.created', label: 'StatefulSet 创建', description: '当创建新的 StatefulSet 时触发' },
    { value: 'statefulset.updated', label: 'StatefulSet 更新', description: '当 StatefulSet 配置更新时触发' },
    { value: 'statefulset.deleted', label: 'StatefulSet 删除', description: '当删除 StatefulSet 时触发' },
    { value: 'service.created', label: 'Service 创建', description: '当创建新的 Service 时触发' },
    { value: 'service.updated', label: 'Service 更新', description: '当 Service 配置更新时触发' },
    { value: 'service.deleted', label: 'Service 删除', description: '当删除 Service 时触发' },
    { value: 'pod.created', label: 'Pod 创建', description: '当创建新的 Pod 时触发' },
    { value: 'pod.updated', label: 'Pod 状态变化', description: '当 Pod 状态发生变化时触发' },
    { value: 'pod.deleted', label: 'Pod 删除', description: '当删除 Pod 时触发' },
];
