// 更加细分的日期格式化工具函数
// 规则：
// - 1分钟内：刚刚
// - 1小时内：xx分钟前
// - 24小时内：xx小时前
// - 7天内：xx天前
// - 30天内：xx周前
// - 365天内：xx月前
// - 超过1年：yyyy-MM-dd

export function formatDate(date: string | null): string {
    if (!date) return '从未';
    const now = new Date();
    const targetDate = new Date(date);
    const diffMs = now.getTime() - targetDate.getTime();
    if (diffMs < 0) return '未来';

    const diffInMinutes = Math.floor(diffMs / (1000 * 60));
    const diffInHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffInWeeks = Math.floor(diffInDays / 7);
    const diffInMonths = Math.floor(diffInDays / 30);
    const diffInYears = Math.floor(diffInDays / 365);

    if (diffInMinutes < 1) {
        return '刚刚';
    } else if (diffInMinutes < 60) {
        return `${diffInMinutes}分钟前`;
    } else if (diffInHours < 24) {
        return `${diffInHours}小时前`;
    } else if (diffInDays < 7) {
        return `${diffInDays}天前`;
    } else if (diffInDays < 30) {
        return `${diffInWeeks}周前`;
    } else if (diffInDays < 365) {
        return `${diffInMonths}月前`;
    } else {
        return targetDate.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
    }
}
