<?php

namespace App\Service;

use App\ClusterLabel;
use App\DTOs\DeploymentDTO;
use App\Exceptions\Deployment\DeploymentNotFoundException;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\K8s\ResourceConflictException;
use App\Exceptions\K8s\ResourceValidationException;
use App\Exceptions\Secret\SecretNotFoundException;
use App\Exceptions\Storage\StorageNotFoundException;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class DeploymentService
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取 Deployment 列表
     */
    public function getDeployments(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments");

            $items = $response->json('items', []);

            return array_map(function ($item) {
                return DeploymentDTO::fromK8sResource($item);
            }, $items);
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('获取 Deployment 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 Deployment 列表', $e);
        } catch (\Exception $e) {
            Log::error('获取 Deployment 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Deployment 列表失败：'.$e->getMessage());
        }
    }

    /**
     * 获取单个 Deployment
     */
    public function getDeployment(string $name): DeploymentDTO
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments/{$name}");

            return DeploymentDTO::fromK8sResource($response->json());
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('Deployment 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'deployment_name' => $name,
                ]);

                throw new DeploymentNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('获取 Deployment 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法获取 Deployment 信息', $e);
        } catch (\Exception $e) {
            Log::error('获取 Deployment 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('获取 Deployment 失败：'.$e->getMessage());
        }
    }

    /**
     * 创建 Deployment
     */
    public function createDeployment(array $data): DeploymentDTO
    {
        // 验证 image pull secrets
        if (! empty($data['image_pull_secrets'])) {
            $this->validateImagePullSecrets($data['image_pull_secrets']);
        }

        // 从容器的 volume_mounts 中收集所有卷挂载信息进行验证
        $allVolumeMounts = [];
        foreach ($data['containers'] ?? [] as $container) {
            if (! empty($container['volume_mounts'])) {
                $allVolumeMounts = array_merge($allVolumeMounts, $container['volume_mounts']);
            }
        }

        // 验证存储挂载
        if (! empty($allVolumeMounts)) {
            $this->validateVolumeMounts($allVolumeMounts);
        }

        $payload = $this->buildDeploymentPayload($data);

        try {
            $response = $this->workspace->cluster->http()
                ->post("/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments", $payload);

            Log::info('Deployment 创建成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $data['name'],
            ]);

            return DeploymentDTO::fromK8sResource($response->json());
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 409) {
                Log::warning('Deployment 名称冲突', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'deployment_name' => $data['name'],
                ]);

                throw new ResourceConflictException("Deployment '{$data['name']}' 已存在");
            }

            if ($e->response && $e->response->status() === 422) {
                $errorBody = $e->response->json();
                $validationErrors = [];

                if (isset($errorBody['details']['causes'])) {
                    foreach ($errorBody['details']['causes'] as $cause) {
                        $validationErrors[] = $cause['message'] ?? $cause['reason'] ?? '未知验证错误';
                    }
                }

                Log::warning('Deployment 配置验证失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'deployment_name' => $data['name'],
                    'validation_errors' => $validationErrors,
                ]);

                throw new ResourceValidationException('Deployment 配置验证失败', $validationErrors);
            }

            Log::error('创建 Deployment 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $data['name'],
                'error' => $e->getMessage(),
                'response' => $e->response ? $e->response->body() : null,
                'payload' => $payload,
            ]);

            throw new K8sConnectionException('无法创建 Deployment', $e);
        } catch (\Exception $e) {
            Log::error('创建 Deployment 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $data['name'],
                'error' => $e->getMessage(),
                'payload' => $payload,
            ]);

            throw new \Exception('创建 Deployment 失败：'.$e->getMessage());
        }
    }

    /**
     * 更新 Deployment
     */
    public function updateDeployment(string $name, array $data): DeploymentDTO
    {
        // 验证 image pull secrets
        if (! empty($data['image_pull_secrets'])) {
            $this->validateImagePullSecrets($data['image_pull_secrets']);
        }

        // 从容器的 volume_mounts 中收集所有卷挂载信息进行验证
        $allVolumeMounts = [];
        foreach ($data['containers'] ?? [] as $container) {
            if (! empty($container['volume_mounts'])) {
                $allVolumeMounts = array_merge($allVolumeMounts, $container['volume_mounts']);
            }
        }

        // 验证存储挂载
        if (! empty($allVolumeMounts)) {
            $this->validateVolumeMounts($allVolumeMounts);
        }

        try {
            // 先获取现有的 Deployment
            $existing = $this->workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments/{$name}")
                ->json();

            // 构建更新的 payload
            $payload = $this->buildDeploymentPayload($data, $existing);

            $response = $this->workspace->cluster->http()
                ->put("/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments/{$name}", $payload);

            Log::info('Deployment 更新成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
            ]);

            return DeploymentDTO::fromK8sResource($response->json());
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('Deployment 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'deployment_name' => $name,
                ]);

                throw new DeploymentNotFoundException($name, $this->workspace->namespace);
            }

            if ($e->response && $e->response->status() === 422) {
                $errorBody = $e->response->json();
                $validationErrors = [];

                if (isset($errorBody['details']['causes'])) {
                    foreach ($errorBody['details']['causes'] as $cause) {
                        $validationErrors[] = $cause['message'] ?? $cause['reason'] ?? '未知验证错误';
                    }
                }

                Log::warning('Deployment 配置验证失败', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'deployment_name' => $name,
                    'validation_errors' => $validationErrors,
                ]);

                throw new ResourceValidationException('Deployment 配置验证失败', $validationErrors);
            }

            Log::error('更新 Deployment 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法更新 Deployment', $e);
        } catch (\Exception $e) {
            Log::error('更新 Deployment 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('更新 Deployment 失败：'.$e->getMessage());
        }
    }

    /**
     * 删除 Deployment
     */
    public function deleteDeployment(string $name): bool
    {
        try {
            $this->workspace->cluster->http()
                ->delete("/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments/{$name}");

            Log::info('Deployment 删除成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
            ]);

            return true;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('Deployment 不存在，视为删除成功', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'deployment_name' => $name,
                ]);

                throw new DeploymentNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('删除 Deployment 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法删除 Deployment', $e);
        } catch (\Exception $e) {
            Log::error('删除 Deployment 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('删除 Deployment 失败：'.$e->getMessage());
        }
    }

    /**
     * 扩容/缩容 Deployment
     */
    public function scaleDeployment(string $name, int $replicas): DeploymentDTO
    {
        if ($replicas < 0) {
            throw new \Exception('副本数不能小于 0');
        }

        try {
            // 使用 PATCH 方式更新副本数
            $patchData = [
                'spec' => [
                    'replicas' => $replicas,
                ],
            ];

            $response = $this->workspace->cluster->http()
                ->replaceHeaders(['Content-Type' => 'application/merge-patch+json'])
                ->patch("/apis/apps/v1/namespaces/{$this->workspace->namespace}/deployments/{$name}", $patchData);

            Log::info('Deployment 扩容成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
                'replicas' => $replicas,
            ]);

            return DeploymentDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('扩容 Deployment 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'deployment_name' => $name,
                'replicas' => $replicas,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('扩容 Deployment 失败：'.$e->getMessage());
        }
    }

    /**
     * 构建 Deployment 载荷
     */
    protected function buildDeploymentPayload(array $data, ?array $existing = null): array
    {
        $containers = $this->buildContainers($data['containers'] ?? []);

        // 从容器的 volume_mounts 中收集所有卷信息
        $allVolumeMounts = [];
        foreach ($data['containers'] ?? [] as $container) {
            if (! empty($container['volume_mounts'])) {
                $allVolumeMounts = array_merge($allVolumeMounts, $container['volume_mounts']);
            }
        }

        $volumes = $this->buildVolumes($allVolumeMounts, $data['containers'] ?? []);
        $imagePullSecrets = $this->buildImagePullSecrets($data['image_pull_secrets'] ?? []);

        $payload = [
            'apiVersion' => 'apps/v1',
            'kind' => 'Deployment',
            'metadata' => [
                'name' => $data['name'],
                'namespace' => $this->workspace->namespace,
                'labels' => array_merge(
                    $this->workspace->buildDefaultLabels(null, $data['name']),
                    [
                        ClusterLabel::WORKLOAD_TYPE->value => ClusterLabel::WORKLOAD_TYPE_DEPLOYMENT->value,
                        ClusterLabel::WORKLOAD_NAME->value => $data['name'],
                    ],
                    $data['labels'] ?? []
                ),
            ],
            'spec' => [
                'replicas' => $data['replicas'] ?? 1,
                'strategy' => [
                    'type' => $data['strategy'] ?? 'RollingUpdate',
                    'rollingUpdate' => [
                        'maxUnavailable' => '25%',
                        'maxSurge' => '25%',
                    ],
                ],
                'selector' => [
                    'matchLabels' => [
                        'app' => $data['name'],
                    ],
                ],
                'template' => [
                    'metadata' => [
                        'labels' => array_merge(
                            $this->workspace->buildDefaultLabels(null, $data['name']),
                            [
                                'app' => $data['name'],
                                ClusterLabel::WORKLOAD_TYPE->value => ClusterLabel::WORKLOAD_TYPE_DEPLOYMENT->value,
                                ClusterLabel::WORKLOAD_NAME->value => $data['name'],
                            ],
                            $data['labels'] ?? []
                        ),
                    ],
                    'spec' => [
                        'containers' => $containers,
                        'restartPolicy' => 'Always', // 强制设置为 Always，Deployment 不允许其他重启策略
                    ],
                ],
            ],
        ];

        // 添加卷
        if (! empty($volumes)) {
            $payload['spec']['template']['spec']['volumes'] = $volumes;
        }

        // 添加 image pull secrets
        if (! empty($imagePullSecrets)) {
            $payload['spec']['template']['spec']['imagePullSecrets'] = $imagePullSecrets;
        }

        // 如果是更新，保留元数据
        if ($existing) {
            $payload['metadata'] = array_merge($existing['metadata'], $payload['metadata']);
        }

        return $payload;
    }

    /**
     * 构建容器配置
     */
    protected function buildContainers(array $containers): array
    {
        return array_map(function ($container) {
            $containerSpec = [
                'name' => $container['name'],
                'image' => $container['image'],
                'stdin' => true,
                'tty' => true,
                'imagePullPolicy' => $container['image_pull_policy'] ?? 'IfNotPresent',
            ];

            // 添加工作目录
            if (! empty($container['working_dir'])) {
                $containerSpec['workingDir'] = $container['working_dir'];
            }

            // 添加 command 和 args
            if (! empty($container['command'])) {
                $containerSpec['command'] = $container['command'];
            }
            if (! empty($container['args'])) {
                $containerSpec['args'] = $container['args'];
            }

            // 添加端口
            if (! empty($container['ports'])) {
                $containerSpec['ports'] = array_map(function ($port) {
                    return [
                        'name' => $port['name'] ?? 'default',
                        'containerPort' => (int) $port['container_port'],
                        'protocol' => $port['protocol'] ?? 'TCP',
                    ];
                }, $container['ports']);
            }

            // 构建环境变量
            $envVars = [];
            $envFrom = [];

            // 添加直接环境变量
            if (! empty($container['env'])) {
                foreach ($container['env'] as $env) {
                    $envVars[] = [
                        'name' => $env['name'],
                        'value' => (string) $env['value'],
                    ];
                }
            }

            // 添加从 ConfigMap 引用的环境变量
            if (! empty($container['env_from_configmap'])) {
                foreach ($container['env_from_configmap'] as $envFromConfig) {
                    // 跳过无效配置（configmap_name 为空）
                    if (empty($envFromConfig['configmap_name'])) {
                        continue;
                    }

                    if (empty($envFromConfig['key'])) {
                        // 引用整个 ConfigMap - 使用 envFrom
                        $envFrom[] = [
                            'configMapRef' => [
                                'name' => $envFromConfig['configmap_name'],
                            ],
                        ];
                    } else {
                        // 引用 ConfigMap 中的特定键 - 使用 env
                        $envName = $envFromConfig['env_name'] ?: $envFromConfig['key'];
                        if ($envName) {
                            $envVars[] = [
                                'name' => $envName,
                                'valueFrom' => [
                                    'configMapKeyRef' => [
                                        'name' => $envFromConfig['configmap_name'],
                                        'key' => $envFromConfig['key'],
                                    ],
                                ],
                            ];
                        }
                    }
                }
            }

            // 添加从 Secret 引用的环境变量
            if (! empty($container['env_from_secret'])) {
                foreach ($container['env_from_secret'] as $envFromSecret) {
                    // 跳过无效配置（secret_name 为空）
                    if (empty($envFromSecret['secret_name'])) {
                        continue;
                    }

                    if (empty($envFromSecret['key'])) {
                        // 引用整个 Secret - 使用 envFrom
                        $envFrom[] = [
                            'secretRef' => [
                                'name' => $envFromSecret['secret_name'],
                            ],
                        ];
                    } else {
                        // 引用 Secret 中的特定键 - 使用 env
                        $envName = $envFromSecret['env_name'] ?: $envFromSecret['key'];
                        if ($envName) {
                            $envVars[] = [
                                'name' => $envName,
                                'valueFrom' => [
                                    'secretKeyRef' => [
                                        'name' => $envFromSecret['secret_name'],
                                        'key' => $envFromSecret['key'],
                                    ],
                                ],
                            ];
                        }
                    }
                }
            }

            if (! empty($envVars)) {
                $containerSpec['env'] = $envVars;
            }

            if (! empty($envFrom)) {
                $containerSpec['envFrom'] = $envFrom;
            }

            // 添加资源限制
            if (! empty($container['resources'])) {
                $resources = $this->buildResourceRequirements($container['resources']);
                if (! empty($resources)) {
                    $containerSpec['resources'] = $resources;
                }
            }

            // 添加健康检查探针
            if (! empty($container['liveness_probe'])) {
                $containerSpec['livenessProbe'] = $this->buildProbe($container['liveness_probe']);
            }
            if (! empty($container['readiness_probe'])) {
                $containerSpec['readinessProbe'] = $this->buildProbe($container['readiness_probe']);
            }
            if (! empty($container['startup_probe'])) {
                $containerSpec['startupProbe'] = $this->buildProbe($container['startup_probe']);
            }

            // 构建卷挂载
            $volumeMounts = [];

            // 添加存储卷挂载
            if (! empty($container['volume_mounts'])) {
                foreach ($container['volume_mounts'] as $mount) {
                    $volumeMount = [
                        'name' => $mount['storage_name'], // 使用storage_name作为name
                        'mountPath' => $mount['mount_path'],
                        'readOnly' => $mount['read_only'] ?? false,
                    ];

                    // 添加subPath支持
                    if (! empty($mount['sub_path'])) {
                        $volumeMount['subPath'] = $mount['sub_path'];
                    }

                    $volumeMounts[] = $volumeMount;
                }
            }

            // 添加 ConfigMap 文件挂载
            if (! empty($container['configmap_mounts'])) {
                foreach ($container['configmap_mounts'] as $mount) {
                    $volumeMounts[] = [
                        'name' => "configmap-{$mount['configmap_name']}-volume",
                        'mountPath' => $mount['mount_path'],
                        'readOnly' => true,
                    ];
                }
            }

            // 添加 Secret 文件挂载
            if (! empty($container['secret_mounts'])) {
                foreach ($container['secret_mounts'] as $mount) {
                    $volumeMounts[] = [
                        'name' => "secret-{$mount['secret_name']}-volume",
                        'mountPath' => $mount['mount_path'],
                        'readOnly' => true,
                    ];
                }
            }

            if (! empty($volumeMounts)) {
                $containerSpec['volumeMounts'] = $volumeMounts;
            }

            return $containerSpec;
        }, $containers);
    }

    /**
     * 构建资源需求
     */
    protected function buildResourceRequirements(array $resources): array
    {
        $result = [];

        if (isset($resources['memory']) || isset($resources['cpu'])) {
            $limits = [];
            $requests = [];

            if (isset($resources['memory'])) {
                $memory = (int) $resources['memory'];
                if ($memory < 512 || $memory % 512 !== 0) {
                    throw new \Exception('内存必须是 512Mi 的倍数且不小于 512Mi');
                }
                $limits['memory'] = $memory.'Mi';
                $requests['memory'] = (int) ($memory / 2).'Mi';
            }

            if (isset($resources['cpu'])) {
                $cpu = (int) $resources['cpu'];
                if ($cpu < 500 || $cpu % 500 !== 0) {
                    throw new \Exception('CPU 必须是 500m 的倍数且不小于 500m');
                }
                $limits['cpu'] = $cpu.'m';
                $requests['cpu'] = (int) ($cpu / 2).'m';
            }

            // 添加 ephemeral-storage 限制
            $ephemeralStorage = config('k8s.resources.ephemeral_storage');
            if ($ephemeralStorage) {
                $limits['ephemeral-storage'] = $ephemeralStorage;
                if (! config('k8s.resources.disable_requests')) {
                    $requests['ephemeral-storage'] = $ephemeralStorage;
                }
            }

            if (! empty($limits)) {
                $result['limits'] = $limits;
            }

            if (! empty($requests) && ! config('k8s.resources.disable_requests')) {
                $result['requests'] = $requests;
            }
        }

        return $result;
    }

    /**
     * 构建卷配置
     */
    protected function buildVolumes(array $volumeMounts, array $containers = []): array
    {
        $volumes = [];
        $processedStorages = [];
        $processedConfigMaps = [];
        $processedSecrets = [];

        // 处理存储卷挂载
        foreach ($volumeMounts as $mount) {
            $storageName = $mount['storage_name'];

            // 避免重复添加同一个存储卷
            if (! in_array($storageName, $processedStorages)) {
                $volumes[] = [
                    'name' => $storageName,
                    'persistentVolumeClaim' => [
                        'claimName' => $storageName,
                    ],
                ];
                $processedStorages[] = $storageName;
            }
        }

        // 处理 ConfigMap 和 Secret 卷
        foreach ($containers as $container) {
            // 处理 ConfigMap 文件挂载
            if (! empty($container['configmap_mounts'])) {
                foreach ($container['configmap_mounts'] as $mount) {
                    $configMapName = $mount['configmap_name'];

                    if (! in_array($configMapName, $processedConfigMaps)) {
                        $volume = [
                            'name' => "configmap-{$configMapName}-volume",
                            'configMap' => [
                                'name' => $configMapName,
                            ],
                        ];

                        // 添加文件权限
                        if (isset($mount['default_mode'])) {
                            $decimalMode = convertOctalPermissionToDecimal($mount['default_mode']);
                            if ($decimalMode !== null) {
                                $volume['configMap']['defaultMode'] = $decimalMode;
                            }
                        }

                        // 添加文件项
                        if (! empty($mount['items'])) {
                            $volume['configMap']['items'] = array_map(function ($item) {
                                return [
                                    'key' => $item['key'],
                                    'path' => $item['path'],
                                ];
                            }, $mount['items']);
                        }

                        $volumes[] = $volume;
                        $processedConfigMaps[] = $configMapName;
                    }
                }
            }

            // 处理 Secret 文件挂载
            if (! empty($container['secret_mounts'])) {
                foreach ($container['secret_mounts'] as $mount) {
                    $secretName = $mount['secret_name'];

                    if (! in_array($secretName, $processedSecrets)) {
                        $volume = [
                            'name' => "secret-{$secretName}-volume",
                            'secret' => [
                                'secretName' => $secretName,
                            ],
                        ];

                        // 添加文件权限
                        if (isset($mount['default_mode'])) {
                            $decimalMode = convertOctalPermissionToDecimal($mount['default_mode']);
                            if ($decimalMode !== null) {
                                $volume['secret']['defaultMode'] = $decimalMode;
                            }
                        }

                        // 添加文件项
                        if (! empty($mount['items'])) {
                            $volume['secret']['items'] = array_map(function ($item) {
                                return [
                                    'key' => $item['key'],
                                    'path' => $item['path'],
                                ];
                            }, $mount['items']);
                        }

                        $volumes[] = $volume;
                        $processedSecrets[] = $secretName;
                    }
                }
            }
        }

        return $volumes;
    }

    /**
     * 构建镜像拉取密钥
     */
    protected function buildImagePullSecrets(array $secrets): array
    {
        return array_map(function ($secret) {
            return ['name' => $secret];
        }, $secrets);
    }

    /**
     * 验证镜像拉取密钥是否存在
     */
    protected function validateImagePullSecrets(array $secrets): void
    {
        foreach ($secrets as $secretName) {
            try {
                $this->workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$secretName}");
            } catch (\Illuminate\Http\Client\RequestException $e) {
                if ($e->response && $e->response->status() === 404) {
                    throw new SecretNotFoundException($secretName, $this->workspace->namespace);
                }
                throw new K8sConnectionException('无法验证镜像拉取密钥', $e);
            } catch (\Exception $e) {
                throw new \Exception("验证镜像拉取密钥失败：{$e->getMessage()}");
            }
        }
    }

    /**
     * 验证存储卷挂载
     */
    protected function validateVolumeMounts(array $volumeMounts): void
    {
        foreach ($volumeMounts as $mount) {
            if (empty($mount['storage_name'])) {
                throw new ResourceValidationException('存储名称不能为空');
            }

            // 验证存储是否存在
            try {
                $this->workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$this->workspace->namespace}/persistentvolumeclaims/{$mount['storage_name']}");
            } catch (\Illuminate\Http\Client\RequestException $e) {
                if ($e->response && $e->response->status() === 404) {
                    throw new StorageNotFoundException($mount['storage_name'], $this->workspace->namespace);
                }
                throw new K8sConnectionException('无法验证存储卷', $e);
            } catch (\Exception $e) {
                throw new \Exception("验证存储卷失败：{$e->getMessage()}");
            }
        }
    }

    /**
     * 构建健康检查探针
     */
    protected function buildProbe(array $probe): array
    {
        $result = [];

        // 设置通用参数
        if (isset($probe['initial_delay_seconds'])) {
            $result['initialDelaySeconds'] = (int) $probe['initial_delay_seconds'];
        }
        if (isset($probe['period_seconds'])) {
            $result['periodSeconds'] = (int) $probe['period_seconds'];
        }
        if (isset($probe['timeout_seconds'])) {
            $result['timeoutSeconds'] = (int) $probe['timeout_seconds'];
        }
        if (isset($probe['success_threshold'])) {
            $result['successThreshold'] = (int) $probe['success_threshold'];
        }
        if (isset($probe['failure_threshold'])) {
            $result['failureThreshold'] = (int) $probe['failure_threshold'];
        }

        // 根据类型设置特定配置
        switch ($probe['type']) {
            case 'http':
                $httpGet = [
                    'path' => $probe['http_path'] ?? '/',
                    'port' => (int) ($probe['http_port'] ?? 80),
                    'scheme' => $probe['http_scheme'] ?? 'HTTP',
                ];

                if (! empty($probe['http_headers'])) {
                    $httpGet['httpHeaders'] = array_map(function ($header) {
                        return [
                            'name' => $header['name'],
                            'value' => $header['value'],
                        ];
                    }, $probe['http_headers']);
                }

                $result['httpGet'] = $httpGet;
                break;

            case 'tcp':
                $result['tcpSocket'] = [
                    'port' => (int) ($probe['tcp_port'] ?? 80),
                ];
                break;

            case 'exec':
                $result['exec'] = [
                    'command' => $probe['exec_command'] ?? [],
                ];
                break;
        }

        return $result;
    }
}
