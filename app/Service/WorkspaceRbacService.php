<?php

namespace App\Service;

use App\Models\Workspace;
use Exception;
use Illuminate\Support\Facades\Log;

class WorkspaceRbacService
{
    /**
     * 为 Workspace 创建完整的 RBAC 权限
     */
    public function createWorkspaceRbac(Workspace $workspace): bool
    {
        if (! config('k8s.rbac.enabled', true)) {
            Log::info('RBAC 功能已禁用，跳过创建', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
            ]);

            return true;
        }

        try {
            // 1. 创建 Service Account
            if (! $this->createServiceAccount($workspace)) {
                Log::error('Service Account 创建失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $workspace->namespace,
                ]);

                return false;
            }

            // 2. 创建 Role
            if (! $this->createRole($workspace)) {
                Log::error('Role 创建失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $workspace->namespace,
                ]);

                return false;
            }

            // 3. 创建 RoleBinding
            if (! $this->createRoleBinding($workspace)) {
                Log::error('RoleBinding 创建失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $workspace->namespace,
                ]);

                return false;
            }

            Log::info('Workspace RBAC 权限创建成功', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Workspace RBAC 权限创建异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * 创建 Service Account
     */
    private function createServiceAccount(Workspace $workspace): bool
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;
            $config = config('k8s.rbac.serviceAccount');

            $serviceAccount = [
                'apiVersion' => 'v1',
                'kind' => 'ServiceAccount',
                'metadata' => [
                    'name' => $config['name'],
                    'namespace' => $namespace,
                    'labels' => array_merge(
                        $workspace->buildDefaultLabels($workspace, 'service-account'),
                        [
                            'rbac.authorization.k8s.io/service-account' => $config['name'],
                        ]
                    ),
                    'annotations' => [
                        'description' => "Service Account for workspace {$workspace->name}",
                        'created-by' => 'paas-platform',
                    ],
                ],
                'automountServiceAccountToken' => $config['automountServiceAccountToken'],
            ];

            $response = $cluster->http()->post("/api/v1/namespaces/{$namespace}/serviceaccounts", $serviceAccount);

            if ($response->successful()) {
                Log::info('Service Account 创建成功', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'service_account' => $config['name'],
                ]);

                return true;
            }

            Log::error('Service Account 创建失败', [
                'workspace_id' => $workspace->id,
                'namespace' => $namespace,
                'response' => $response->body(),
                'status' => $response->status(),
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('Service Account 创建异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 创建 Role
     */
    private function createRole(Workspace $workspace): bool
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;
            $config = config('k8s.rbac.role');

            $role = [
                'apiVersion' => 'rbac.authorization.k8s.io/v1',
                'kind' => 'Role',
                'metadata' => [
                    'name' => $config['name'],
                    'namespace' => $namespace,
                    'labels' => array_merge(
                        $workspace->buildDefaultLabels($workspace, 'role'),
                        [
                            'rbac.authorization.k8s.io/role' => $config['name'],
                        ]
                    ),
                    'annotations' => [
                        'description' => "Role for workspace {$workspace->name}",
                        'created-by' => 'paas-platform',
                    ],
                ],
                'rules' => $config['rules'],
            ];

            $response = $cluster->http()->post("/apis/rbac.authorization.k8s.io/v1/namespaces/{$namespace}/roles", $role);

            if ($response->successful()) {
                Log::info('Role 创建成功', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'role' => $config['name'],
                ]);

                return true;
            }

            Log::error('Role 创建失败', [
                'workspace_id' => $workspace->id,
                'namespace' => $namespace,
                'response' => $response->body(),
                'status' => $response->status(),
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('Role 创建异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 创建 RoleBinding
     */
    private function createRoleBinding(Workspace $workspace): bool
    {
        try {
            $cluster = $workspace->cluster;
            $namespace = $workspace->namespace;
            $config = config('k8s.rbac.roleBinding');
            $serviceAccountConfig = config('k8s.rbac.serviceAccount');
            $roleConfig = config('k8s.rbac.role');

            $roleBinding = [
                'apiVersion' => 'rbac.authorization.k8s.io/v1',
                'kind' => 'RoleBinding',
                'metadata' => [
                    'name' => $config['name'],
                    'namespace' => $namespace,
                    'labels' => array_merge(
                        $workspace->buildDefaultLabels($workspace, 'role-binding'),
                        [
                            'rbac.authorization.k8s.io/role-binding' => $config['name'],
                        ]
                    ),
                    'annotations' => [
                        'description' => "RoleBinding for workspace {$workspace->name}",
                        'created-by' => 'paas-platform',
                    ],
                ],
                'subjects' => [
                    [
                        'kind' => 'ServiceAccount',
                        'name' => $serviceAccountConfig['name'],
                        'namespace' => $namespace,
                    ],
                ],
                'roleRef' => [
                    'kind' => 'Role',
                    'name' => $roleConfig['name'],
                    'apiGroup' => 'rbac.authorization.k8s.io',
                ],
            ];

            $response = $cluster->http()->post("/apis/rbac.authorization.k8s.io/v1/namespaces/{$namespace}/rolebindings", $roleBinding);

            if ($response->successful()) {
                Log::info('RoleBinding 创建成功', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'role_binding' => $config['name'],
                ]);

                return true;
            }

            Log::error('RoleBinding 创建失败', [
                'workspace_id' => $workspace->id,
                'namespace' => $namespace,
                'response' => $response->body(),
                'status' => $response->status(),
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('RoleBinding 创建异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 删除 Workspace 的 RBAC 权限
     */
    public function deleteWorkspaceRbac(Workspace $workspace): bool
    {
        if (! config('k8s.rbac.enabled', true)) {
            return true;
        }

        $success = true;
        $cluster = $workspace->cluster;
        $namespace = $workspace->namespace;

        try {
            // 删除 RoleBinding
            $roleBindingConfig = config('k8s.rbac.roleBinding');
            $response = $cluster->http()->delete("/apis/rbac.authorization.k8s.io/v1/namespaces/{$namespace}/rolebindings/{$roleBindingConfig['name']}");
            if (! $response->successful() && $response->status() !== 404) {
                Log::warning('RoleBinding 删除失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'role_binding' => $roleBindingConfig['name'],
                    'status' => $response->status(),
                ]);
                $success = false;
            }

            // 删除 Role
            $roleConfig = config('k8s.rbac.role');
            $response = $cluster->http()->delete("/apis/rbac.authorization.k8s.io/v1/namespaces/{$namespace}/roles/{$roleConfig['name']}");
            if (! $response->successful() && $response->status() !== 404) {
                Log::warning('Role 删除失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'role' => $roleConfig['name'],
                    'status' => $response->status(),
                ]);
                $success = false;
            }

            // 删除 Service Account
            $serviceAccountConfig = config('k8s.rbac.serviceAccount');
            $response = $cluster->http()->delete("/api/v1/namespaces/{$namespace}/serviceaccounts/{$serviceAccountConfig['name']}");
            if (! $response->successful() && $response->status() !== 404) {
                Log::warning('Service Account 删除失败', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                    'service_account' => $serviceAccountConfig['name'],
                    'status' => $response->status(),
                ]);
                $success = false;
            }

            if ($success) {
                Log::info('Workspace RBAC 权限删除成功', [
                    'workspace_id' => $workspace->id,
                    'namespace' => $namespace,
                ]);
            }

            return $success;
        } catch (Exception $e) {
            Log::error('Workspace RBAC 权限删除异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * 检查 Workspace RBAC 权限是否存在
     */
    public function checkWorkspaceRbac(Workspace $workspace): array
    {
        $cluster = $workspace->cluster;
        $namespace = $workspace->namespace;
        $status = [
            'service_account' => false,
            'role' => false,
            'role_binding' => false,
        ];

        try {
            // 检查 Service Account
            $serviceAccountConfig = config('k8s.rbac.serviceAccount');
            $response = $cluster->http()->get("/api/v1/namespaces/{$namespace}/serviceaccounts/{$serviceAccountConfig['name']}");
            $status['service_account'] = $response->successful();

            // 检查 Role
            $roleConfig = config('k8s.rbac.role');
            $response = $cluster->http()->get("/apis/rbac.authorization.k8s.io/v1/namespaces/{$namespace}/roles/{$roleConfig['name']}");
            $status['role'] = $response->successful();

            // 检查 RoleBinding
            $roleBindingConfig = config('k8s.rbac.roleBinding');
            $response = $cluster->http()->get("/apis/rbac.authorization.k8s.io/v1/namespaces/{$namespace}/rolebindings/{$roleBindingConfig['name']}");
            $status['role_binding'] = $response->successful();
        } catch (Exception $e) {
            Log::error('检查 Workspace RBAC 权限状态异常', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
                'error' => $e->getMessage(),
            ]);
        }

        return $status;
    }

    /**
     * 同步单个 Workspace 的 RBAC 权限
     */
    public function syncWorkspaceRbac(Workspace $workspace): bool
    {
        Log::info('开始同步 Workspace RBAC 权限', [
            'workspace_id' => $workspace->id,
            'namespace' => $workspace->namespace,
        ]);

        $status = $this->checkWorkspaceRbac($workspace);
        $needsUpdate = false;

        // 检查缺失的权限
        if (! $status['service_account']) {
            Log::info('Service Account 不存在，需要创建', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
            ]);
            $needsUpdate = true;
        }

        if (! $status['role']) {
            Log::info('Role 不存在，需要创建', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
            ]);
            $needsUpdate = true;
        }

        if (! $status['role_binding']) {
            Log::info('RoleBinding 不存在，需要创建', [
                'workspace_id' => $workspace->id,
                'namespace' => $workspace->namespace,
            ]);
            $needsUpdate = true;
        }

        if ($needsUpdate) {
            return $this->createWorkspaceRbac($workspace);
        }

        Log::info('Workspace RBAC 权限已存在，无需同步', [
            'workspace_id' => $workspace->id,
            'namespace' => $workspace->namespace,
        ]);

        return true;
    }
}
