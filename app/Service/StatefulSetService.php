<?php

namespace App\Service;

use App\ClusterLabel;
use App\DTOs\StatefulSetDTO;
use App\Exceptions\K8s\K8sConnectionException;
use App\Exceptions\Secret\SecretNotFoundException;
use App\Exceptions\Service\ServiceNotFoundException;
use App\Exceptions\StatefulSet\StatefulSetConflictException;
use App\Exceptions\StatefulSet\StatefulSetNotFoundException;
use App\Exceptions\Storage\StorageNotFoundException;
use App\Models\Workspace;
use Illuminate\Support\Facades\Log;

class StatefulSetService
    use \App\Traits\K8sWorkloadBuilderTrait;
{
    public function __construct(
        protected Workspace $workspace
    ) {}

    /**
     * 获取 StatefulSet 列表
     */
    public function getStatefulSets(): array
    {
        try {
            $response = $this->workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets");

            $items = $response->json('items', []);

            return array_map(function ($item) {
                return StatefulSetDTO::fromK8sResource($item);
            }, $items);
        } catch (\Exception $e) {
            Log::error('获取 StatefulSet 列表失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'error' => $e->getMessage(),

    /**
     * 创建 StatefulSet
     */
    public function createStatefulSet(array $data): StatefulSetDTO
    {
        // 验证 image pull secrets
        if (! empty($data['image_pull_secrets'])) {
            $this->validateImagePullSecrets($data['image_pull_secrets']);
        }

        // 从容器的 volume_mounts 中收集所有卷挂载信息进行验证
        $allVolumeMounts = [];
        foreach ($data['containers'] ?? [] as $container) {
            if (! empty($container['volume_mounts'])) {
                $allVolumeMounts = array_merge($allVolumeMounts, $container['volume_mounts']);
            }
        }

        // 验证存储挂载
        if (! empty($allVolumeMounts)) {
            $this->validateVolumeMounts($allVolumeMounts);
        }

        // 处理 headless service
        if (! empty($data['service_name'])) {
            // 用户指定了 service_name，检测是否存在
            try {
                $this->validateService($data['service_name']);
                // 服务存在，直接使用
            } catch (ServiceNotFoundException $e) {
                // 服务不存在，创建 headless service
                $this->createHeadlessService($data['name'], $data['service_name'], $data['containers'] ?? []);
            }
        } else {
            // 用户没有指定 service_name，使用自动生成的名称
            $autoServiceName = $data['name'].'-headless';
            try {
                $this->validateService($autoServiceName);
                // 自动生成的服务存在，直接使用
                $data['service_name'] = $autoServiceName;
            } catch (ServiceNotFoundException $e) {
                // 自动生成的服务不存在，创建它
                $this->createHeadlessService($data['name'], $autoServiceName, $data['containers'] ?? []);
                $data['service_name'] = $autoServiceName;
            }
        }

        $payload = $this->buildStatefulSetPayload($data);

        try {
            $response = $this->workspace->cluster->http()
                ->post("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets", $payload);

            Log::info('StatefulSet 创建成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $data['name'],
            ]);

            return StatefulSetDTO::fromK8sResource($response->json());
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 409) {
                Log::warning('StatefulSet 名称冲突', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'statefulset_name' => $data['name'],
                ]);

                throw new StatefulSetConflictException($data['name']);
            }

            Log::error('创建 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $data['name'],
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法创建 StatefulSet', $e);
        } catch (\Exception $e) {
            Log::error('创建 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $data['name'],
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('创建 StatefulSet 失败：'.$e->getMessage());
        }
    }

    /**
     * 更新 StatefulSet
     */
    public function updateStatefulSet(string $name, array $data): StatefulSetDTO
    {
        // 验证 image pull secrets
        if (! empty($data['image_pull_secrets'])) {
            $this->validateImagePullSecrets($data['image_pull_secrets']);
        }

        // 从容器的 volume_mounts 中收集所有卷挂载信息进行验证
        $allVolumeMounts = [];
        foreach ($data['containers'] ?? [] as $container) {
            if (! empty($container['volume_mounts'])) {
                $allVolumeMounts = array_merge($allVolumeMounts, $container['volume_mounts']);
            }
        }

        // 验证存储挂载
        if (! empty($allVolumeMounts)) {
            $this->validateVolumeMounts($allVolumeMounts);
        }

        try {
            // 先获取现有的 StatefulSet
            $existing = $this->workspace->cluster->http()
                ->get("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}")
                ->json();

            // 构建更新的 payload
            $payload = $this->buildStatefulSetPayload($data, $existing);

            $response = $this->workspace->cluster->http()
                ->put("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}", $payload);

            // 更新对应的 headless service 端口（如果存在）
            $serviceName = $payload['spec']['serviceName'] ?? null;
            if ($serviceName) {
                $this->updateHeadlessService($serviceName, $data['containers'] ?? []);
            }

            Log::info('StatefulSet 更新成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
            ]);

            return StatefulSetDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('更新 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('更新 StatefulSet 失败：'.$e->getMessage());
        }
    }

    /**
     * 删除 StatefulSet
     */
    public function deleteStatefulSet(string $name): bool
    {
        try {
            $this->workspace->cluster->http()
                ->delete("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}");

            Log::info('StatefulSet 删除成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
            ]);

            return true;
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                Log::warning('StatefulSet 不存在', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'statefulset_name' => $name,
                ]);

                throw new StatefulSetNotFoundException($name, $this->workspace->namespace);
            }

            Log::error('删除 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('无法删除 StatefulSet', $e);
        } catch (\Exception $e) {
            Log::error('删除 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'error' => $e->getMessage(),
            ]);

            throw new K8sConnectionException('删除 StatefulSet 失败：'.$e->getMessage());
        }
    }

    /**
     * 扩容/缩容 StatefulSet
     */
    public function scaleStatefulSet(string $name, int $replicas): StatefulSetDTO
    {
        if ($replicas < 0) {
            throw new \Exception('副本数不能小于 0');
        }

        try {
            // 使用 PATCH 方式更新副本数
            $patchData = [
                'spec' => [
                    'replicas' => $replicas,
                ],
            ];

            $response = $this->workspace->cluster->http()
                ->replaceHeaders(['Content-Type' => 'application/merge-patch+json'])
                ->patch("/apis/apps/v1/namespaces/{$this->workspace->namespace}/statefulsets/{$name}", $patchData);

            Log::info('StatefulSet 扩容成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'replicas' => $replicas,
            ]);

            return StatefulSetDTO::fromK8sResource($response->json());
        } catch (\Exception $e) {
            Log::error('扩容 StatefulSet 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'statefulset_name' => $name,
                'replicas' => $replicas,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('扩容 StatefulSet 失败：'.$e->getMessage());
        }
    }

    /**
     * 构建 StatefulSet 载荷
     */
    protected function buildStatefulSetPayload(array $data, ?array $existing = null): array
    {
        $containers = $this->buildContainers($data['containers'] ?? []);

        // 从容器的 volume_mounts 中收集所有卷信息
        $allVolumeMounts = [];
        foreach ($data['containers'] ?? [] as $container) {
            if (! empty($container['volume_mounts'])) {
                $allVolumeMounts = array_merge($allVolumeMounts, $container['volume_mounts']);
            }
        }

        $volumes = $this->buildVolumes($allVolumeMounts, $data['containers'] ?? []);
        $imagePullSecrets = $this->buildImagePullSecrets($data['image_pull_secrets'] ?? []);

        $payload = [
            'apiVersion' => 'apps/v1',
            'kind' => 'StatefulSet',
            'metadata' => [
                'name' => $data['name'],
                'namespace' => $this->workspace->namespace,
                'labels' => array_merge(
                    $this->workspace->buildDefaultLabels(null, $data['name']),
                    [
                        ClusterLabel::WORKLOAD_TYPE->value => ClusterLabel::WORKLOAD_TYPE_STATEFULSET->value,
                        ClusterLabel::WORKLOAD_NAME->value => $data['name'],
                    ],
                    $data['labels'] ?? []
                ),
            ],
            'spec' => [
                'replicas' => $data['replicas'] ?? 1,
                'serviceName' => $data['service_name'] ?? $data['name'].'-headless',
                'updateStrategy' => [
                    'type' => $data['update_strategy'] ?? 'RollingUpdate',
                    'rollingUpdate' => [
                        'partition' => 0,
                    ],
                ],
                'selector' => [
                    'matchLabels' => [
                        'app' => $data['name'],
                    ],
                ],
                'template' => [
                    'metadata' => [
                        'labels' => array_merge(
                            $this->workspace->buildDefaultLabels(null, $data['name']),
                            [
                                'app' => $data['name'],
                                ClusterLabel::WORKLOAD_TYPE->value => ClusterLabel::WORKLOAD_TYPE_STATEFULSET->value,
                                ClusterLabel::WORKLOAD_NAME->value => $data['name'],
                            ],
                            $data['labels'] ?? []
                        ),
                    ],
                    'spec' => [
                        'containers' => $containers,
                        'restartPolicy' => 'Always', // 强制设置为 Always，StatefulSet 不允许其他重启策略
                    ],
                ],
            ],
        ];

        // 添加卷
        if (! empty($volumes)) {
            $payload['spec']['template']['spec']['volumes'] = $volumes;
        }

        // 添加 image pull secrets
        if (! empty($imagePullSecrets)) {
            $payload['spec']['template']['spec']['imagePullSecrets'] = $imagePullSecrets;
        }

        // 添加卷声明模板
        if (! empty($volumeClaimTemplates)) {
            $payload['spec']['volumeClaimTemplates'] = $volumeClaimTemplates;
        }

        // 如果是更新，保留元数据和现有卷声明模板
        if ($existing) {
            $payload['metadata'] = array_merge($existing['metadata'], $payload['metadata']);
            // StatefulSet 的卷声明模板不能修改，保留原有的
            if (isset($existing['spec']['volumeClaimTemplates'])) {
                $payload['spec']['volumeClaimTemplates'] = $existing['spec']['volumeClaimTemplates'];
            }
        }

        return $payload;
    }

    /**
     * 构建容器配置（复用 DeploymentService 的逻辑）
     */
    protected function buildContainers(array $containers): array
    {
        return array_map(function ($container) {
            $containerSpec = [
                'name' => $container['name'],
                'image' => $container['image'],
                'stdin' => true,
                'tty' => true,
                'imagePullPolicy' => $container['image_pull_policy'] ?? 'IfNotPresent',
            ];

            // 添加工作目录
            if (! empty($container['working_dir'])) {
                $containerSpec['workingDir'] = $container['working_dir'];
            }

            // 添加 command 和 args
            if (! empty($container['command'])) {
                $containerSpec['command'] = $container['command'];
            }
            if (! empty($container['args'])) {
                $containerSpec['args'] = $container['args'];
            }

            // 添加端口
            if (! empty($container['ports'])) {
                $containerSpec['ports'] = array_map(function ($port) {
                    return [
                        'name' => $port['name'] ?? 'default',
                        'containerPort' => (int) $port['container_port'],
                        'protocol' => $port['protocol'] ?? 'TCP',
                    ];
                }, $container['ports']);
            }

            // 构建环境变量
            $envVars = [];
            $envFrom = [];

            // 添加直接环境变量
            if (! empty($container['env'])) {
                foreach ($container['env'] as $env) {
                    $envVars[] = [
                        'name' => $env['name'],
                        'value' => (string) $env['value'],
                    ];
                }
            }

            // 添加从 ConfigMap 引用的环境变量
            if (! empty($container['env_from_configmap'])) {
                foreach ($container['env_from_configmap'] as $envFromConfig) {
                    // 跳过无效配置（configmap_name 为空）
                    if (empty($envFromConfig['configmap_name'])) {
                        continue;
                    }

                    if (empty($envFromConfig['key'])) {
                        // 引用整个 ConfigMap - 使用 envFrom
                        $envFrom[] = [
                            'configMapRef' => [
                                'name' => $envFromConfig['configmap_name'],
                            ],
                        ];
                    } else {
                        // 引用 ConfigMap 中的特定键 - 使用 env
                        $envName = $envFromConfig['env_name'] ?: $envFromConfig['key'];
                        if ($envName) {
                            $envVars[] = [
                                'name' => $envName,
                                'valueFrom' => [
                                    'configMapKeyRef' => [
                                        'name' => $envFromConfig['configmap_name'],
                                        'key' => $envFromConfig['key'],
                                    ],
                                ],
                            ];
                        }
                    }
                }
            }

            // 添加从 Secret 引用的环境变量
            if (! empty($container['env_from_secret'])) {
                foreach ($container['env_from_secret'] as $envFromSecret) {
                    // 跳过无效配置（secret_name 为空）
                    if (empty($envFromSecret['secret_name'])) {
                        continue;
                    }

                    if (empty($envFromSecret['key'])) {
                        // 引用整个 Secret - 使用 envFrom
                        $envFrom[] = [
                            'secretRef' => [
                                'name' => $envFromSecret['secret_name'],
                            ],
                        ];
                    } else {
                        // 引用 Secret 中的特定键 - 使用 env
                        $envName = $envFromSecret['env_name'] ?: $envFromSecret['key'];
                        if ($envName) {
                            $envVars[] = [
                                'name' => $envName,
                                'valueFrom' => [
                                    'secretKeyRef' => [
                                        'name' => $envFromSecret['secret_name'],
                                        'key' => $envFromSecret['key'],
                                    ],
                                ],
                            ];
                        }
                    }
                }
            }

            if (! empty($envVars)) {
                $containerSpec['env'] = $envVars;
            }

            if (! empty($envFrom)) {
                $containerSpec['envFrom'] = $envFrom;
            }

            // 添加资源限制
            if (! empty($container['resources'])) {
                $resources = $this->buildResourceRequirements($container['resources']);
                if (! empty($resources)) {
                    $containerSpec['resources'] = $resources;
                }
            }

            // 添加健康检查探针
            if (! empty($container['liveness_probe'])) {
                $containerSpec['livenessProbe'] = $this->buildProbe($container['liveness_probe']);
            }
            if (! empty($container['readiness_probe'])) {
                $containerSpec['readinessProbe'] = $this->buildProbe($container['readiness_probe']);
            }
            if (! empty($container['startup_probe'])) {
                $containerSpec['startupProbe'] = $this->buildProbe($container['startup_probe']);
            }

            // 构建卷挂载
            $volumeMounts = [];

            // 添加存储卷挂载
            if (! empty($container['volume_mounts'])) {
                foreach ($container['volume_mounts'] as $mount) {
                    $volumeMount = [
                        'name' => $mount['storage_name'], // 使用storage_name作为name
                        'mountPath' => $mount['mount_path'],
                        'readOnly' => $mount['read_only'] ?? false,
                    ];

                    // 添加subPath支持
                    if (! empty($mount['sub_path'])) {
                        $volumeMount['subPath'] = $mount['sub_path'];
                    }

                    $volumeMounts[] = $volumeMount;
                }
            }

            // 添加 ConfigMap 文件挂载
            if (! empty($container['configmap_mounts'])) {
                foreach ($container['configmap_mounts'] as $mount) {
                    $volumeMounts[] = [
                        'name' => "configmap-{$mount['configmap_name']}-volume",
                        'mountPath' => $mount['mount_path'],
                        'readOnly' => true,
                    ];
                }
            }

            // 添加 Secret 文件挂载
            if (! empty($container['secret_mounts'])) {
                foreach ($container['secret_mounts'] as $mount) {
                    $volumeMounts[] = [
                        'name' => "secret-{$mount['secret_name']}-volume",
                        'mountPath' => $mount['mount_path'],
                        'readOnly' => true,
                    ];
                }
            }

            if (! empty($volumeMounts)) {
                $containerSpec['volumeMounts'] = $volumeMounts;
            }

            return $containerSpec;
        }, $containers);
    }

    /**
     * 构建资源需求
     */
    protected function buildResourceRequirements(array $resources): array
    {
        $result = [];

        if (isset($resources['memory']) || isset($resources['cpu'])) {
            $limits = [];
            $requests = [];

            if (isset($resources['memory'])) {
                $memory = (int) $resources['memory'];
                if ($memory < 512 || $memory % 512 !== 0) {
                    throw new \Exception('内存必须是 512Mi 的倍数且不小于 512Mi');
                }
                $limits['memory'] = $memory.'Mi';
                $requests['memory'] = (int) ($memory / 2).'Mi';
            }

            if (isset($resources['cpu'])) {
                $cpu = (int) $resources['cpu'];
                if ($cpu < 500 || $cpu % 500 !== 0) {
                    throw new \Exception('CPU 必须是 500m 的倍数且不小于 500m');
                }
                $limits['cpu'] = $cpu.'m';
                $requests['cpu'] = (int) ($cpu / 2).'m';
            }

            if (! empty($limits)) {
                $result['limits'] = $limits;
            }
            if (! empty($requests) && ! config('k8s.resources.disable_requests')) {
                $result['requests'] = $requests;
            }
        }

        //TODO: 为 limits 和 requests 都加入 ephemeral-storage 并且设置为 config('k8s.resources.ephemeral_storage')，如果 disable_requests，则不设置 request，只设置 limits。
        $ephemeralStorage = config('k8s.resources.ephemeral_storage');
        if (isset($result['limits']) && $ephemeralStorage) {
            $result['limits']['ephemeral-storage'] = $ephemeralStorage;
        }
        if (isset($result['requests']) && $ephemeralStorage) {
            $result['requests']['ephemeral-storage'] = $ephemeralStorage;
        }

        return $result;
    }

    /**
     * 构建卷配置
     */
    protected function buildVolumes(array $volumeMounts, array $containers = []): array
    {
        $volumes = [];
        $processedStorages = [];
        $processedConfigMaps = [];
        $processedSecrets = [];

        // 处理存储卷挂载
        foreach ($volumeMounts as $mount) {
            $storageName = $mount['storage_name'];

            // 避免重复添加同一个存储卷
            if (! in_array($storageName, $processedStorages)) {
                $volumes[] = [
                    'name' => $storageName,
                    'persistentVolumeClaim' => [
                        'claimName' => $storageName,
                    ],
                ];
                $processedStorages[] = $storageName;
            }
        }

        // 处理 ConfigMap 和 Secret 卷
        foreach ($containers as $container) {
            // 处理 ConfigMap 文件挂载
            if (! empty($container['configmap_mounts'])) {
                foreach ($container['configmap_mounts'] as $mount) {
                    $configMapName = $mount['configmap_name'];

                    if (! in_array($configMapName, $processedConfigMaps)) {
                        $volume = [
                            'name' => "configmap-{$configMapName}-volume",
                            'configMap' => [
                                'name' => $configMapName,
                            ],
                        ];

                        // 添加文件权限
                        if (isset($mount['default_mode'])) {
                            $decimalMode = convertOctalPermissionToDecimal($mount['default_mode']);
                            if ($decimalMode !== null) {
                                $volume['configMap']['defaultMode'] = $decimalMode;
                            }
                        }

                        // 添加文件项
                        if (! empty($mount['items'])) {
                            $volume['configMap']['items'] = array_map(function ($item) {
                                return [
                                    'key' => $item['key'],
                                    'path' => $item['path'],
                                ];
                            }, $mount['items']);
                        }

                        $volumes[] = $volume;
                        $processedConfigMaps[] = $configMapName;
                    }
                }
            }

            // 处理 Secret 文件挂载
            if (! empty($container['secret_mounts'])) {
                foreach ($container['secret_mounts'] as $mount) {
                    $secretName = $mount['secret_name'];

                    if (! in_array($secretName, $processedSecrets)) {
                        $volume = [
                            'name' => "secret-{$secretName}-volume",
                            'secret' => [
                                'secretName' => $secretName,
                            ],
                        ];

                        // 添加文件权限
                        if (isset($mount['default_mode'])) {
                            $decimalMode = convertOctalPermissionToDecimal($mount['default_mode']);
                            if ($decimalMode !== null) {
                                $volume['secret']['defaultMode'] = $decimalMode;
                            }
                        }

                        // 添加文件项
                        if (! empty($mount['items'])) {
                            $volume['secret']['items'] = array_map(function ($item) {
                                return [
                                    'key' => $item['key'],
                                    'path' => $item['path'],
                                ];
                            }, $mount['items']);
                        }

                        $volumes[] = $volume;
                        $processedSecrets[] = $secretName;
                    }
                }
            }
        }

        return $volumes;
    }

    /**
     * 构建镜像拉取密钥
     */
    protected function buildImagePullSecrets(array $secrets): array
    {
        return array_map(function ($secret) {
            return ['name' => $secret];
        }, $secrets);
    }

    /**
     * 更新 Headless Service 端口
     */
    protected function updateHeadlessService(string $serviceName, array $containers): void
    {
        try {
            // 先获取现有的服务
            $existingService = $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/services/{$serviceName}")
                ->json();

            // 收集新的端口配置
            $newPorts = $this->collectServicePorts($containers);

            // 构建更新 payload
            $payload = [
                'apiVersion' => 'v1',
                'kind' => 'Service',
                'metadata' => $existingService['metadata'],
                'spec' => array_merge($existingService['spec'], [
                    'ports' => $newPorts,
                ]),
            ];

            // 更新服务
            $this->workspace->cluster->http()
                ->put("/api/v1/namespaces/{$this->workspace->namespace}/services/{$serviceName}", $payload);

            Log::info('Headless Service 端口更新成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'service_name' => $serviceName,
                'ports_count' => count($newPorts),
            ]);
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                // 服务不存在，记录日志但不抛出异常
                Log::info('Headless Service 不存在，跳过端口更新', [
                    'workspace_id' => $this->workspace->id,
                    'namespace' => $this->workspace->namespace,
                    'service_name' => $serviceName,
                ]);

                return;
            }

            Log::warning('更新 Headless Service 端口失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'service_name' => $serviceName,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('更新 Headless Service 端口失败：'.$e->getMessage());
        } catch (\Exception $e) {
            Log::warning('更新 Headless Service 端口失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'service_name' => $serviceName,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('更新 Headless Service 端口失败：'.$e->getMessage());
        }
    }

    /**
     * 从容器配置中收集服务端口
     */
    protected function collectServicePorts(array $containers): array
    {
        $servicePorts = [];
        $usedPorts = [];

        foreach ($containers as $container) {
            if (! empty($container['ports'])) {
                foreach ($container['ports'] as $port) {
                    $containerPort = (int) $port['container_port'];
                    $protocol = $port['protocol'] ?? 'TCP';
                    $portName = $port['name'] ?? null;

                    // 避免重复端口
                    $portKey = $containerPort.'-'.$protocol;
                    if (in_array($portKey, $usedPorts)) {
                        continue;
                    }
                    $usedPorts[] = $portKey;

                    // 生成端口名称
                    if (empty($portName)) {
                        $portName = strtolower($protocol).'-'.$containerPort;
                    }

                    $servicePorts[] = [
                        'name' => $portName,
                        'port' => $containerPort,
                        'targetPort' => $containerPort,
                        'protocol' => $protocol,
                    ];
                }
            }
        }

        // 如果没有定义任何端口，使用默认端口
        if (empty($servicePorts)) {
            $servicePorts[] = [
                'name' => 'http',
                'port' => 80,
                'targetPort' => 80,
                'protocol' => 'TCP',
            ];
        }

        return $servicePorts;
    }

    /**
     * 创建 Headless Service
     */
    protected function createHeadlessService(string $appName, ?string $serviceName = null, array $containers = []): void
    {
        $serviceName = $serviceName ?? $appName.'-headless';

        $labels = array_merge(
            $this->workspace->buildDefaultLabels(null, $serviceName),
            [
                ClusterLabel::WORKLOAD_TYPE->value => ClusterLabel::WORKLOAD_TYPE_SERVICE->value,
                ClusterLabel::WORKLOAD_NAME->value => $serviceName,
            ]
        );

        // 从容器配置中收集所有端口
        $servicePorts = $this->collectServicePorts($containers);

        $payload = [
            'apiVersion' => 'v1',
            'kind' => 'Service',
            'metadata' => [
                'name' => $serviceName,
                'namespace' => $this->workspace->namespace,
                'labels' => $labels,
            ],
            'spec' => [
                'clusterIP' => 'None',
                'selector' => [
                    'app' => $appName,
                ],
                'ports' => $servicePorts,
            ],
        ];

        try {
            $this->workspace->cluster->http()
                ->post("/api/v1/namespaces/{$this->workspace->namespace}/services", $payload);

            Log::info('Headless Service 创建成功', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'service_name' => $serviceName,
                'app_name' => $appName,
                'ports_count' => count($servicePorts),
            ]);
        } catch (\Exception $e) {
            Log::warning('创建 Headless Service 失败', [
                'workspace_id' => $this->workspace->id,
                'namespace' => $this->workspace->namespace,
                'service_name' => $serviceName,
                'app_name' => $appName,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('创建 Headless Service 失败：'.$e->getMessage());
        }
    }

    /**
     * 验证镜像拉取密钥是否存在
     */
    protected function validateImagePullSecrets(array $secrets): void
    {
        foreach ($secrets as $secretName) {
            try {
                $this->workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$this->workspace->namespace}/secrets/{$secretName}");
            } catch (\Illuminate\Http\Client\RequestException $e) {
                if ($e->response && $e->response->status() === 404) {
                    throw new SecretNotFoundException($secretName, $this->workspace->namespace);
                }
                throw new K8sConnectionException('无法验证镜像拉取密钥', $e);
            } catch (\Exception $e) {
                throw new \Exception("验证镜像拉取密钥失败：{$e->getMessage()}");
            }
        }
    }

    /**
     * 验证存储卷挂载
     */
    protected function validateVolumeMounts(array $volumeMounts): void
    {
        foreach ($volumeMounts as $mount) {
            if (empty($mount['storage_name'])) {
                throw new \Exception('存储名称不能为空');
            }

            // 验证存储是否存在
            try {
                $this->workspace->cluster->http()
                    ->get("/api/v1/namespaces/{$this->workspace->namespace}/persistentvolumeclaims/{$mount['storage_name']}");
            } catch (\Illuminate\Http\Client\RequestException $e) {
                if ($e->response && $e->response->status() === 404) {
                    throw new StorageNotFoundException($mount['storage_name'], $this->workspace->namespace);
                }
                throw new K8sConnectionException('无法验证存储卷', $e);
            } catch (\Exception $e) {
                throw new \Exception("验证存储卷失败：{$e->getMessage()}");
            }
        }
    }

    /**
     * 验证卷声明模板
     */
    protected function validateVolumeClaimTemplates(array $templates): void
    {
        foreach ($templates as $template) {
            if (empty($template['name'])) {
                throw new \Exception('卷声明模板名称不能为空');
            }

            if (empty($template['size']) || ! is_numeric($template['size'])) {
                throw new \Exception('卷声明模板容量必须为数字');
            }
        }
    }

    /**
     * 验证服务是否存在
     */
    protected function validateService(string $serviceName): void
    {
        try {
            $this->workspace->cluster->http()
                ->get("/api/v1/namespaces/{$this->workspace->namespace}/services/{$serviceName}");
        } catch (\Illuminate\Http\Client\RequestException $e) {
            if ($e->response && $e->response->status() === 404) {
                throw new ServiceNotFoundException($serviceName, $this->workspace->namespace);
            }
            throw new K8sConnectionException('无法验证服务', $e);
        }
    }

    /**
     * 构建健康检查探针
     */
    protected function buildProbe(array $probe): array
    {
        $result = [];

        // 设置通用参数
        if (isset($probe['initial_delay_seconds'])) {
            $result['initialDelaySeconds'] = (int) $probe['initial_delay_seconds'];
        }
        if (isset($probe['period_seconds'])) {
            $result['periodSeconds'] = (int) $probe['period_seconds'];
        }
        if (isset($probe['timeout_seconds'])) {
            $result['timeoutSeconds'] = (int) $probe['timeout_seconds'];
        }
        if (isset($probe['success_threshold'])) {
            $result['successThreshold'] = (int) $probe['success_threshold'];
        }
        if (isset($probe['failure_threshold'])) {
            $result['failureThreshold'] = (int) $probe['failure_threshold'];
        }

        // 根据类型设置特定配置
        switch ($probe['type']) {
            case 'http':
                $httpGet = [
                    'path' => $probe['http_path'] ?? '/',
                    'port' => (int) ($probe['http_port'] ?? 80),
                    'scheme' => $probe['http_scheme'] ?? 'HTTP',
                ];

                if (! empty($probe['http_headers'])) {
                    $httpGet['httpHeaders'] = array_map(function ($header) {
                        return [
                            'name' => $header['name'],
                            'value' => $header['value'],
                        ];
                    }, $probe['http_headers']);
                }

                $result['httpGet'] = $httpGet;
                break;

            case 'tcp':
                $result['tcpSocket'] = [
                    'port' => (int) ($probe['tcp_port'] ?? 80),
                ];
                break;

            case 'exec':
                $result['exec'] = [
                    'command' => $probe['exec_command'] ?? [],
                ];
                break;
        }

        return $result;
    }
}
