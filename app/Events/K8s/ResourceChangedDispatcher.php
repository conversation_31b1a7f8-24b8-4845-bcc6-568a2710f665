<?php

namespace App\Events\K8s;

use App\Events\ResourceChanged;
use App\Models\Cluster;

class ResourceChangedDispatcher
{
    /**
     * Dispatch both the general ResourceChanged event and specific K8s events
     * This maintains backward compatibility while providing detailed events
     */
    public static function dispatch(
        string $namespace,
        Cluster $cluster,
        string $resourceType,
        array $changes
    ): void {
        // Always dispatch the general ResourceChanged event for backward compatibility
        event(new ResourceChanged(
            $namespace,
            $cluster->name,
            $cluster->id,
            $resourceType,
            $changes
        ));

        // Dispatch specific K8s events for each change
        self::dispatchSpecificEvents($namespace, $cluster, $resourceType, $changes);
    }

    /**
     * Dispatch specific K8s events for each resource change
     */
    private static function dispatchSpecificEvents(
        string $namespace,
        Cluster $cluster,
        string $resourceType,
        array $changes
    ): void {
        // Handle created resources
        if (! empty($changes['created'])) {
            foreach ($changes['created'] as $resource) {
                $event = K8sEventFactory::create(
                    $resourceType,
                    'created',
                    $namespace,
                    $cluster,
                    $resource
                );

                if ($event) {
                    event($event);
                }
            }
        }

        // Handle updated resources
        if (! empty($changes['updated'])) {
            foreach ($changes['updated'] as $resource) {
                // For updated events, we need both current and previous resource
                // The CacheK8sResourcesCommand should provide this data
                $previousResource = $resource['_previous'] ?? [];
                unset($resource['_previous']); // Remove the previous data from current resource

                $event = K8sEventFactory::create(
                    $resourceType,
                    'updated',
                    $namespace,
                    $cluster,
                    $resource,
                    $previousResource
                );

                if ($event) {
                    event($event);
                }
            }
        }

        // Handle deleted resources
        if (! empty($changes['deleted'])) {
            foreach ($changes['deleted'] as $resource) {
                $event = K8sEventFactory::create(
                    $resourceType,
                    'deleted',
                    $namespace,
                    $cluster,
                    $resource
                );

                if ($event) {
                    event($event);
                }
            }
        }
    }

    /**
     * Dispatch a single resource change event
     * This is useful for webhook scenarios where we get individual resource changes
     */
    public static function dispatchSingle(
        string $namespace,
        Cluster $cluster,
        string $resourceType,
        string $action,
        array $resource,
        array $previousResource = []
    ): void {
        // Dispatch the general ResourceChanged event
        event(new ResourceChanged(
            $namespace,
            $cluster->name,
            $cluster->id,
            $resourceType,
            [$action => [$resource]]
        ));

        // Dispatch the specific K8s event
        $event = K8sEventFactory::create(
            $resourceType,
            $action,
            $namespace,
            $cluster,
            $resource,
            $previousResource
        );

        if ($event) {
            event($event);
        }
    }
}
