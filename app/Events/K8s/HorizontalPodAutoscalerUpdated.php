<?php

namespace App\Events\K8s;

class HorizontalPodAutoscalerUpdated extends BaseK8sResourceEvent
{
    public array $previousResource;

    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceName,
        array $resource,
        array $previousResource = []
    ) {
        parent::__construct(
            $namespace,
            $clusterName,
            $clusterId,
            'horizontalpodautoscaler',
            $resourceName,
            $resource,
            'updated'
        );

        $this->previousResource = $previousResource;
    }

    public function broadcastWith(): array
    {
        $data = parent::broadcastWith();
        $data['previous_resource'] = $this->previousResource;

        return $data;
    }
}
