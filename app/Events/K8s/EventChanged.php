<?php

namespace App\Events\K8s;

/**
 * Generic event for any K8s event changes.
 * This maintains backward compatibility while specific events provide more detail.
 */
class EventChanged extends BaseK8sResourceEvent
{
    public function __construct(
        string $namespace,
        string $clusterName,
        int $clusterId,
        string $resourceName,
        array $resource,
        string $action
    ) {
        parent::__construct(
            $namespace,
            $clusterName,
            $clusterId,
            'event',
            $resourceName,
            $resource,
            $action
        );
    }

    public function broadcastAs(): string
    {
        return 'event.changed';
    }
}
