<?php

namespace App\Events\K8s;

use App\Models\Cluster;

class K8sEventFactory
{
    /**
     * Resource type to event class mapping
     */
    private static array $eventMapping = [
        'deployment' => [
            'created' => DeploymentCreated::class,
            'updated' => DeploymentUpdated::class,
            'deleted' => DeploymentDeleted::class,
            'changed' => DeploymentChanged::class,
        ],
        'statefulset' => [
            'created' => StatefulSetCreated::class,
            'updated' => StatefulSetUpdated::class,
            'deleted' => StatefulSetDeleted::class,
            'changed' => StatefulSetChanged::class,
        ],
        'service' => [
            'created' => ServiceCreated::class,
            'updated' => ServiceUpdated::class,
            'deleted' => ServiceDeleted::class,
            'changed' => ServiceChanged::class,
        ],
        'ingress' => [
            'created' => IngressCreated::class,
            'updated' => IngressUpdated::class,
            'deleted' => IngressDeleted::class,
            'changed' => IngressChanged::class,
        ],
        'pod' => [
            'created' => PodCreated::class,
            'updated' => PodUpdated::class,
            'deleted' => PodDeleted::class,
            'changed' => PodChanged::class,
        ],
        'secret' => [
            'created' => SecretCreated::class,
            'updated' => SecretUpdated::class,
            'deleted' => SecretDeleted::class,
            'changed' => SecretChanged::class,
        ],
        'configmap' => [
            'created' => ConfigMapCreated::class,
            'updated' => ConfigMapUpdated::class,
            'deleted' => ConfigMapDeleted::class,
            'changed' => ConfigMapChanged::class,
        ],
        'persistentvolumeclaim' => [
            'created' => PersistentVolumeClaimCreated::class,
            'updated' => PersistentVolumeClaimUpdated::class,
            'deleted' => PersistentVolumeClaimDeleted::class,
            'changed' => PersistentVolumeClaimChanged::class,
        ],
        'horizontalpodautoscaler' => [
            'created' => HorizontalPodAutoscalerCreated::class,
            'updated' => HorizontalPodAutoscalerUpdated::class,
            'deleted' => HorizontalPodAutoscalerDeleted::class,
            'changed' => HorizontalPodAutoscalerChanged::class,
        ],
        'event' => [
            'created' => EventCreated::class,
            'updated' => EventUpdated::class,
            'deleted' => EventDeleted::class,
            'changed' => EventChanged::class,
        ],
    ];

    /**
     * Create a specific K8s resource event
     */
    public static function create(
        string $resourceType,
        string $action,
        string $namespace,
        Cluster $cluster,
        array $resource,
        array $previousResource = []
    ): ?BaseK8sResourceEvent {
        $resourceName = $resource['metadata']['name'] ?? 'unknown';

        if (! isset(self::$eventMapping[$resourceType][$action])) {
            return null;
        }

        $eventClass = self::$eventMapping[$resourceType][$action];

        // Handle updated events which need previous resource
        if ($action === 'updated' && class_exists($eventClass)) {
            return new $eventClass(
                $namespace,
                $cluster->name,
                $cluster->id,
                $resourceName,
                $resource,
                $previousResource
            );
        }

        // Handle other events (created, deleted, changed)
        if (class_exists($eventClass)) {
            if ($action === 'changed') {
                // Changed events need the action parameter
                $actualAction = self::detectAction($resource, $previousResource);

                return new $eventClass(
                    $namespace,
                    $cluster->name,
                    $cluster->id,
                    $resourceName,
                    $resource,
                    $actualAction
                );
            }

            return new $eventClass(
                $namespace,
                $cluster->name,
                $cluster->id,
                $resourceName,
                $resource
            );
        }

        return null;
    }

    /**
     * Detect the actual action from resource comparison
     */
    private static function detectAction(array $resource, array $previousResource): string
    {
        if (empty($previousResource)) {
            return 'created';
        }

        if (empty($resource)) {
            return 'deleted';
        }

        return 'updated';
    }

    /**
     * Get all supported resource types
     */
    public static function getSupportedResourceTypes(): array
    {
        return array_keys(self::$eventMapping);
    }

    /**
     * Get all supported actions for a resource type
     */
    public static function getSupportedActions(string $resourceType): array
    {
        return array_keys(self::$eventMapping[$resourceType] ?? []);
    }
}
