<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ImageValidationRequest;
use App\Models\Workspace;
use App\Service\ImageValidationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ImageValidationController extends Controller
{
    /**
     * 验证镜像并返回大小等信息
     */
    public function validateImage(ImageValidationRequest $request): JsonResponse
    {
        try {
            $workspace = Workspace::findOrFail($request->input('workspace_id'));

            // 权限校验
            if (Auth::user() && Auth::user()->cannot('view', $workspace)) {
                return $this->forbidden('没有权限访问该工作空间');
            }

            $image = $request->input('image');
            $imagePullSecrets = $request->input('image_pull_secrets', []);

            $service = new ImageValidationService($workspace);
            $result = $service->validateImageSize($image, $imagePullSecrets);

            if (! $result['valid']) {
                // 对错误信息进行友好化处理，不暴露技术细节
                $friendlyError = $this->getFriendlyErrorMessage($result['error']);

                return $this->success([
                    'valid' => false,
                    'message' => $friendlyError,
                ]);
            }

            return $this->success([
                'valid' => true,
                'size_bytes' => $result['size_bytes'],
                'size_mb' => $result['size_mb'],
                'layers_count' => $result['layers_count'],
                'layers' => $result['layers'],
                'message' => '',
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFound('工作空间不存在');
        } catch (\Exception $e) {
            return $this->error('镜像验证失败，请稍后重试', 500);
        }
    }

    /**
     * 将技术错误信息转换为用户友好的提示
     */
    private function getFriendlyErrorMessage(string $error): string
    {
        $error = strtolower($error);

        // 网络连接相关错误
        if (strpos($error, 'curl error') !== false ||
            strpos($error, 'tls connect error') !== false ||
            strpos($error, 'ssl') !== false ||
            strpos($error, 'timeout') !== false ||
            strpos($error, 'connection') !== false) {
            return '无法连接到镜像仓库，请检查镜像地址是否正确';
        }

        // 认证相关错误
        if (strpos($error, '401') !== false ||
            strpos($error, 'unauthorized') !== false ||
            strpos($error, 'authentication') !== false) {
            return '镜像仓库认证失败，请检查拉取密钥是否正确';
        }

        // 镜像不存在
        if (strpos($error, '404') !== false ||
            strpos($error, 'not found') !== false ||
            strpos($error, 'manifest unknown') !== false) {
            return '镜像不存在，请检查镜像名称和标签是否正确';
        }

        // 权限相关错误
        if (strpos($error, '403') !== false ||
            strpos($error, 'forbidden') !== false ||
            strpos($error, 'access denied') !== false) {
            return '没有权限访问此镜像，请检查拉取密钥权限。';
        }

        // 服务器错误
        if (strpos($error, '5') !== false && strpos($error, '00') !== false) {
            return '镜像仓库服务暂时不可用，请稍后重试';
        }

        // 默认通用错误
        return '镜像验证失败，请检查镜像地址是否正确';
    }
}
