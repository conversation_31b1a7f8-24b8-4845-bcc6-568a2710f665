<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ImageValidationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'image' => 'required|string|max:255',
            'workspace_id' => 'required|integer|exists:workspaces,id',
            'image_pull_secrets' => 'nullable|array',
            'image_pull_secrets.*' => 'string|max:63',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'image.required' => '镜像名称不能为空',
            'image.string' => '镜像名称必须是字符串',
            'image.max' => '镜像名称不能超过255个字符',
            'workspace_id.required' => '工作空间ID不能为空',
            'workspace_id.integer' => '工作空间ID必须是整数',
            'workspace_id.exists' => '指定的工作空间不存在',
            'image_pull_secrets.array' => '镜像拉取密钥必须是数组',
            'image_pull_secrets.*.string' => '镜像拉取密钥必须是字符串',
            'image_pull_secrets.*.max' => '镜像拉取密钥不能超过63个字符',
        ];
    }
}
