<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateWebhookEndpointRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('update', $this->route('webhook'));
    }

    public function rules(): array
    {
        // 基于现有 K8s 事件类推断的可用事件
        $availableEvents = [
            '*',
            // K8s 资源事件 - 基于 app/Events/K8s 目录下的事件类
            'deployment.created', 'deployment.updated', 'deployment.deleted',
            'statefulset.created', 'statefulset.updated', 'statefulset.deleted',
            'service.created', 'service.updated', 'service.deleted',
            'pod.created', 'pod.updated', 'pod.deleted',
            'secret.created', 'secret.updated', 'secret.deleted',
            'configmap.created', 'configmap.updated', 'configmap.deleted',
            'ingress.created', 'ingress.updated', 'ingress.deleted',
            'persistentvolumeclaim.created', 'persistentvolumeclaim.updated', 'persistentvolumeclaim.deleted',
            'horizontalpodautoscaler.created', 'horizontalpodautoscaler.updated', 'horizontalpodautoscaler.deleted',
            'event.created', 'event.updated', 'event.deleted',
            // 系统事件
            'test',
        ];

        return [
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:2048',
            'events' => 'required|array|min:1',
            'events.*' => 'string|in:'.implode(',', $availableEvents),
            'headers' => 'nullable|array',
            'headers.*' => 'string|max:1000',
            'is_active' => 'boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required' => '名称不能为空',
            'name.max' => '名称长度不能超过255个字符',
            'url.required' => 'URL不能为空',
            'url.url' => 'URL格式不正确',
            'url.max' => 'URL长度不能超过2048个字符',
            'events.required' => '至少需要选择一个事件类型',
            'events.min' => '至少需要选择一个事件类型',
            'events.*.in' => '无效的事件类型',
            'headers.array' => '请求头必须是数组格式',
            'headers.*.max' => '请求头值长度不能超过1000个字符',
        ];
    }
}
