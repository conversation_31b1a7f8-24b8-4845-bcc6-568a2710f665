<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WebhookDeliveryResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'event_type' => $this->resource->event_type,
            'status' => $this->resource->status,
            'http_status_code' => $this->resource->response_status, // 修复字段名
            'response_body' => $this->resource->response_body,
            'error_message' => $this->resource->error_message,
            'attempt' => $this->resource->attempts, // 修复字段名
            'duration' => null, // 这个字段在数据库中不存在，暂时设为null
            'formatted_duration' => null, // 这个字段在数据库中不存在，暂时设为null
            'payload' => $this->resource->payload,
            'delivered_at' => $this->resource->delivered_at,
            'created_at' => $this->resource->created_at,
            'updated_at' => $this->resource->updated_at,
            'webhook_endpoint_id' => $this->resource->webhook_endpoint_id,
        ];
    }
}
