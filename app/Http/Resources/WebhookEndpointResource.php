<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WebhookEndpointResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $totalDeliveries = $this->deliveries_count ?? 0;
        $successfulDeliveries = $this->successful_deliveries_count ?? 0;

        $successRate = null;
        if ($totalDeliveries > 0) {
            $successRate = round(($successfulDeliveries / $totalDeliveries) * 100, 1);
        }

        return [
            'id' => $this->id,
            'name' => $this->name,
            'url' => $this->url,
            'is_active' => $this->is_active,
            'events' => $this->events,
            'headers' => $this->headers,
            'timeout' => config('webhook-server.timeout_in_seconds', 30),
            'max_attempts' => config('webhook-server.tries', 3),
            'last_delivered_at' => $this->last_delivered_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deliveries_count' => $totalDeliveries,
            'success_rate' => $successRate,
            'secret' => $request->routeIs('webhooks.show', 'webhooks.edit') ? $this->resource->secret : null,
        ];
    }
}
