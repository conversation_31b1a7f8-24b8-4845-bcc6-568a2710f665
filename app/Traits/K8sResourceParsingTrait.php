<?php

namespace App\Traits;

/**
 * Kubernetes 资源解析 Trait
 *
 * 用于在不同的 DTO 类中复用资源解析逻辑
 */
trait K8sResourceParsingTrait
{
    /**
     * 提取容器信息
     */
    protected static function extractContainers(array $containers, array $podVolumes): array
    {
        return array_map(function ($container) use ($podVolumes) {
            $envFrom = static::extractEnvFrom($container);

            return [
                'name' => $container['name'] ?? '',
                'image' => $container['image'] ?? '',
                'working_dir' => $container['workingDir'] ?? null,
                'ports' => static::extractPorts($container),
                'env' => static::extractEnvironmentVariables($container),
                'env_from_configmap' => $envFrom['configmap'],
                'env_from_secret' => $envFrom['secret'],
                'resources' => static::extractResources($container),
                'volume_mounts' => static::extractVolumeMounts($container, $podVolumes),
                'configmap_mounts' => static::extractFileMounts($container, $podVolumes, 'configmap'),
                'secret_mounts' => static::extractFileMounts($container, $podVolumes, 'secret'),
                'command' => $container['command'] ?? null,
                'args' => $container['args'] ?? null,
                'liveness_probe' => static::extractProbe($container['livenessProbe'] ?? null),
                'readiness_probe' => static::extractProbe($container['readinessProbe'] ?? null),
                'startup_probe' => static::extractProbe($container['startupProbe'] ?? null),
                'stdin' => $container['stdin'] ?? false,
                'tty' => $container['tty'] ?? false,
            ];
        }, $containers);
    }

    /**
     * 提取端口信息
     */
    protected static function extractPorts(array $container): array
    {
        $ports = $container['ports'] ?? [];

        return array_map(function ($port) {
            return [
                'name' => $port['name'] ?? 'default',
                'container_port' => $port['containerPort'] ?? 80,
                'protocol' => $port['protocol'] ?? 'TCP',
            ];
        }, $ports);
    }

    /**
     * 提取环境变量引用
     */
    protected static function extractEnvFrom(array $container): array
    {
        $configMapEnvs = [];
        $secretEnvs = [];

        // From envFrom (referencing all keys in a resource)
        if (isset($container['envFrom'])) {
            foreach ($container['envFrom'] as $envFrom) {
                if (isset($envFrom['configMapRef']['name'])) {
                    $configMapEnvs[] = [
                        'configmap_name' => $envFrom['configMapRef']['name'],
                        'key' => null,
                        'env_name' => null,
                    ];
                }
                if (isset($envFrom['secretRef']['name'])) {
                    $secretEnvs[] = [
                        'secret_name' => $envFrom['secretRef']['name'],
                        'key' => null,
                        'env_name' => null,
                    ];
                }
            }
        }

        // From env.valueFrom (referencing a specific key)
        if (isset($container['env'])) {
            foreach ($container['env'] as $envVar) {
                if (isset($envVar['valueFrom']['configMapKeyRef'])) {
                    $configMapEnvs[] = [
                        'configmap_name' => $envVar['valueFrom']['configMapKeyRef']['name'] ?? '',
                        'key' => $envVar['valueFrom']['configMapKeyRef']['key'] ?? '',
                        'env_name' => $envVar['name'] ?? '',
                    ];
                }
                if (isset($envVar['valueFrom']['secretKeyRef'])) {
                    $secretEnvs[] = [
                        'secret_name' => $envVar['valueFrom']['secretKeyRef']['name'] ?? '',
                        'key' => $envVar['valueFrom']['secretKeyRef']['key'] ?? '',
                        'env_name' => $envVar['name'] ?? '',
                    ];
                }
            }
        }

        return ['configmap' => $configMapEnvs, 'secret' => $secretEnvs];
    }

    /**
     * 提取环境变量
     */
    protected static function extractEnvironmentVariables(array $container): array
    {
        $env = $container['env'] ?? [];
        $result = [];

        foreach ($env as $envVar) {
            if (isset($envVar['value'])) {
                $result[] = [
                    'name' => $envVar['name'] ?? '',
                    'value' => $envVar['value'],
                ];
            }
        }

        return $result;
    }

    /**
     * 提取卷挂载
     */
    protected static function extractVolumeMounts(array $container, array $podVolumes): array
    {
        $volumeMounts = $container['volumeMounts'] ?? [];
        $pvcVolumes = [];
        foreach ($podVolumes as $volume) {
            if (isset($volume['persistentVolumeClaim'])) {
                $volumeName = $volume['name'] ?? '';
                $pvcVolumes[$volumeName] = $volume['persistentVolumeClaim']['claimName'] ?? '';
            }
        }

        $pvcMounts = [];
        foreach ($volumeMounts as $mount) {
            $mountName = $mount['name'] ?? '';
            if (array_key_exists($mountName, $pvcVolumes)) {
                $result = [
                    'mount_path' => $mount['mountPath'] ?? '',
                    'storage_name' => $pvcVolumes[$mountName],
                    'read_only' => $mount['readOnly'] ?? false,
                ];
                if (isset($mount['subPath']) && !empty($mount['subPath'])) {
                    $result['sub_path'] = $mount['subPath'];
                } else {
                    $result['sub_path'] = null;
                }
                $pvcMounts[] = $result;
            }
        }

        return $pvcMounts;
    }

    /**
     * 提取文件挂载
     */
    protected static function extractFileMounts(array $container, array $podVolumes, string $type): array
    {
        $mounts = [];
        $volumeMounts = $container['volumeMounts'] ?? [];

        $sourceKey = $type === 'configmap' ? 'configMap' : 'secret';
        $nameKey = $type === 'configmap' ? 'configmap_name' : 'secret_name';

        // Find volumes of the specified type
        $typedVolumes = [];
        foreach ($podVolumes as $volume) {
            if (isset($volume[$sourceKey])) {
                $volumeName = $volume['name'] ?? '';
                $typedVolumes[$volumeName] = $volume[$sourceKey];
            }
        }

        if (empty($typedVolumes)) {
            return [];
        }

        // Find mounts that use these volumes
        foreach ($volumeMounts as $volumeMount) {
            $volumeMountName = $volumeMount['name'] ?? '';
            if (array_key_exists($volumeMountName, $typedVolumes)) {
                $volumeSource = $typedVolumes[$volumeMountName];
                $mountData = [
                    $nameKey => $volumeSource['name'] ?? '',
                    'mount_path' => $volumeMount['mountPath'] ?? '',
                    'read_only' => $volumeMount['readOnly'] ?? false,
                ];

                if (isset($volumeSource['items'])) {
                    $mountData['items'] = array_map(function ($item) {
                        return [
                            'key' => $item['key'],
                            'path' => $item['path'],
                        ];
                    }, $volumeSource['items']);
                }
                if (isset($volumeSource['defaultMode'])) {
                    $mountData['default_mode'] = convertDecimalPermissionToOctal($volumeSource['defaultMode']);
                }

                $mounts[] = $mountData;
            }
        }

        return $mounts;
    }

    /**
     * 提取资源配置
     */
    protected static function extractResources(array $container): array
    {
        $resources = $container['resources'] ?? [];
        $limits = $resources['limits'] ?? [];

        if (empty($resources) || empty($resources['limits'])) {
            return [
                'cpu' => 0,
                'memory' => 0,
                'ephemeral_storage' => 0,
            ];
        }

        return [
            'cpu' => static::parseCpuValue($limits['cpu'] ?? ''),
            'memory' => static::parseMemoryValue($limits['memory'] ?? ''),
            'ephemeral_storage' => static::parseMemoryValue($limits['ephemeral-storage'] ?? ''),
        ];
    }

    /**
     * 提取卷信息
     */
    protected static function extractVolumes(array $volumes): array
    {
        return array_map(function ($volume) {
            $result = [
                'name' => $volume['name'] ?? '',
                'type' => 'unknown',
            ];

            if (isset($volume['persistentVolumeClaim'])) {
                $result['type'] = 'pvc';
                $result['claim_name'] = $volume['persistentVolumeClaim']['claimName'] ?? '';
            } elseif (isset($volume['configMap'])) {
                $result['type'] = 'configmap';
                $result['config_map'] = $volume['configMap']['name'] ?? '';
            } elseif (isset($volume['secret'])) {
                $result['type'] = 'secret';
                $result['secret'] = $volume['secret']['secretName'] ?? '';
            }

            return $result;
        }, $volumes);
    }

    /**
     * 提取镜像拉取密钥
     */
    protected static function extractImagePullSecrets(array $imagePullSecrets): array
    {
        return array_map(function ($secret) {
            return $secret['name'] ?? '';
        }, $imagePullSecrets);
    }

    /**
     * 解析 CPU 值（转换为毫核心）
     */
    protected static function parseCpuValue(string $cpu): int
    {
        if (empty($cpu)) {
            return 0;
        }

        if (str_ends_with($cpu, 'm')) {
            return (int) str_replace('m', '', $cpu);
        }

        return (int) ((float) $cpu * 1000);
    }

    /**
     * 解析内存值（转换为 Mi）
     */
    protected static function parseMemoryValue(string $memory): int
    {
        if (empty($memory)) {
            return 0;
        }

        if (str_ends_with($memory, 'Mi')) {
            return (int) str_replace('Mi', '', $memory);
        }

        if (str_ends_with($memory, 'Gi')) {
            return (int) ((float) str_replace('Gi', '', $memory) * 1024);
        }

        if (str_ends_with($memory, 'Ki')) {
            return (int) ((float) str_replace('Ki', '', $memory) / 1024);
        }

        // 假设是字节，转换为 Mi
        return (int) ((int) $memory / 1024 / 1024);
    }

    /**
     * 解析存储大小（转换为 Mi）
     */
    protected static function parseStorageSize(string $storage): int
    {
        if (empty($storage)) {
            return 0;
        }

        if (str_ends_with($storage, 'Mi')) {
            return (int) str_replace('Mi', '', $storage);
        }

        if (str_ends_with($storage, 'Gi')) {
            return (int) ((float) str_replace('Gi', '', $storage) * 1024);
        }

        if (str_ends_with($storage, 'Ki')) {
            return (int) ((float) str_replace('Ki', '', $storage) / 1024);
        }

        // 假设是字节，转换为 Mi
        return (int) ((int) $storage / 1024 / 1024);
    }

    /**
     * 提取探针信息
     */
    protected static function extractProbe(?array $probe): ?array
    {
        if (empty($probe)) {
            return null;
        }

        $result = [
            'initial_delay_seconds' => $probe['initialDelaySeconds'] ?? 0,
            'period_seconds' => $probe['periodSeconds'] ?? 10,
            'timeout_seconds' => $probe['timeoutSeconds'] ?? 1,
            'success_threshold' => $probe['successThreshold'] ?? 1,
            'failure_threshold' => $probe['failureThreshold'] ?? 3,
        ];

        // 检测探针类型
        if (isset($probe['httpGet'])) {
            $result['type'] = 'http';
            $result['http_path'] = $probe['httpGet']['path'] ?? '/';
            $result['http_port'] = $probe['httpGet']['port'] ?? 80;
            $result['http_scheme'] = $probe['httpGet']['scheme'] ?? 'HTTP';
            $result['http_headers'] = [];

            if (isset($probe['httpGet']['httpHeaders'])) {
                foreach ($probe['httpGet']['httpHeaders'] as $header) {
                    $result['http_headers'][] = [
                        'name' => $header['name'] ?? '',
                        'value' => $header['value'] ?? '',
                    ];
                }
            }
        } elseif (isset($probe['tcpSocket'])) {
            $result['type'] = 'tcp';
            $result['tcp_port'] = $probe['tcpSocket']['port'] ?? 80;
        } elseif (isset($probe['exec'])) {
            $result['type'] = 'exec';
            $result['exec_command'] = $probe['exec']['command'] ?? [];
        } else {
            return null; // 未知的探针类型
        }

        return $result;
    }

    /**
     * 提取卷声明模板（仅用于 StatefulSet）
     */
    protected static function extractVolumeClaimTemplates(array $templates): array
    {
        return array_map(function ($template) {
            $metadata = $template['metadata'] ?? [];
            $spec = $template['spec'] ?? [];

            return [
                'name' => $metadata['name'] ?? '',
                'access_modes' => $spec['accessModes'] ?? [],
                'size' => static::parseStorageSize($spec['resources']['requests']['storage'] ?? ''),
                'storage_class' => $spec['storageClassName'] ?? '',
            ];
        }, $templates);
    }
}
