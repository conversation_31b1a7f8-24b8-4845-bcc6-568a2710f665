<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class WebhookEndpoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'url',
        'events',
        'headers',
        'is_active',
        'secret',
        'last_delivered_at',
        'workspace_id',
    ];

    protected $casts = [
        'events' => 'array',
        'headers' => 'array',
        'is_active' => 'boolean',
        'last_delivered_at' => 'datetime',
    ];

    protected static function booted()
    {
        static::creating(function ($webhook) {
            if (empty($webhook->secret)) {
                $webhook->secret = Str::random(40);
            }
        });
    }

    /**
     * 关联到 workspace
     */
    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }

    /**
     * 关联到 webhook deliveries
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(WebhookDelivery::class);
    }

    /**
     * 检查是否应该接收指定事件
     */
    public function shouldReceiveEvent(string $eventType): bool
    {
        if (! $this->is_active) {
            return false;
        }

        // 如果订阅了所有事件
        if (in_array('*', $this->events)) {
            return true;
        }

        // 检查是否订阅了具体的事件类型
        return in_array($eventType, $this->events);
    }

    /**
     * 重新生成secret
     */
    public function regenerateSecret(): void
    {
        $this->update(['secret' => Str::random(40)]);
    }

    /**
     * 获取成功率
     */
    public function getSuccessRateAttribute(): float
    {
        $totalDeliveries = $this->deliveries()->count();

        if ($totalDeliveries === 0) {
            return 0;
        }

        $successfulDeliveries = $this->deliveries()
            ->where('status', 'success')
            ->count();

        return round(($successfulDeliveries / $totalDeliveries) * 100, 2);
    }

    /**
     * 获取最近的deliveries数量
     */
    public function getDeliveriesCountAttribute(): int
    {
        return $this->deliveries()->count();
    }

    /**
     * 从配置文件获取超时时间（秒）
     */
    public function getTimeoutAttribute(): int
    {
        return config('webhook-server.timeout_in_seconds', 30);
    }

    /**
     * 从配置文件获取最大重试次数
     */
    public function getMaxAttemptsAttribute(): int
    {
        return config('webhook-server.tries', 3);
    }
}
