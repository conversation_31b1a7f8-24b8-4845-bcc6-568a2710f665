<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WebhookDelivery extends Model
{
    use HasFactory;

    protected $fillable = [
        'webhook_endpoint_id',
        'url',
        'event_type',
        'payload',
        'headers',
        'status',
        'attempts',
        'response_status',
        'response_body',
        'error_message',
        'delivered_at',
    ];

    protected $casts = [
        'payload' => 'array',
        'headers' => 'array',
        'delivered_at' => 'datetime',
    ];

    /**
     * 关联到 webhook endpoint
     */
    public function webhookEndpoint(): BelongsTo
    {
        return $this->belongsTo(WebhookEndpoint::class);
    }

    /**
     * 标记为成功
     */
    public function markAsSuccessful(int $responseStatus, string $responseBody): void
    {
        $this->update([
            'status' => 'success',
            'response_status' => $responseStatus,
            'response_body' => $responseBody,
            'delivered_at' => now(),
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(string $errorMessage, ?int $nextRetryIn = null): void
    {
        $this->increment('attempts');

        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
        ]);
    }
}
