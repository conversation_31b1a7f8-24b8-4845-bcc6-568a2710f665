<?php

namespace App\Listeners;

use App\Contracts\WebhookEventInterface;
use App\Events\K8s\BaseK8sResourceEvent;
use App\Notifications\WebhookNotification;
use Illuminate\Support\Facades\Log;

class WebhookEventListener
{
    /**
     * Handle the event.
     */
    public function handle(WebhookEventInterface $event): void
    {
        try {
            // 构建 webhook 负载
            $payload = $event->getEventPayload();

            $eventType = $event->getEventType();

            $workspace = $event->getWorkspace();

            if (empty($workspace)) {
                return;
            }

            // 合并 timestamp, unique_id, event_type
            $payload = array_merge($payload, [
                'event_id' => uniqid(),
                'event_type' => $eventType,
                'timestamp' => now()->toISOString(),
            ]);

            // 发送通知
            $notification = new WebhookNotification($eventType, $payload);
            $workspace->notify($notification);

            Log::debug('Webhook notification sent', [
                'event_type' => $eventType,
                'workspace_id' => $workspace->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Error processing webhook event', [
                'error' => $e->getMessage(),
                'event_type' => $eventType ?? 'unknown',
                'trace' => $e->getTraceAsString(),
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(BaseK8sResourceEvent $event, \Throwable $exception): void
    {
        Log::error('Webhook event listener failed', [
            'event_type' => "{$event->resourceType}.{$event->action}",
            'namespace' => $event->namespace,
            'error' => $exception->getMessage(),
        ]);
    }
}
