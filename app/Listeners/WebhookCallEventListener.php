<?php

namespace App\Listeners;

use App\Models\WebhookDelivery;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\WebhookServer\Events\FinalWebhookCallFailedEvent;
use Spa<PERSON>\WebhookServer\Events\WebhookCallFailedEvent;
use Spa<PERSON>\WebhookServer\Events\WebhookCallSucceededEvent;

class WebhookCallEventListener
{
    /**
     * Handle webhook call succeeded event.
     */
    public function handleWebhookCallSucceeded(WebhookCallSucceededEvent $event): void
    {
        $this->updateDeliveryStatus($event, 'success');

        Log::info('Webhook call succeeded', [
            'url' => $event->webhookUrl,
            'event_type' => $event->meta['event_type'] ?? 'unknown',
            'attempt' => $event->attempt,
            'response_status' => $event->response?->getStatusCode(),
            'uuid' => $event->uuid,
        ]);
    }

    /**
     * Handle webhook call failed event.
     */
    public function handleWebhookCallFailed(WebhookCallFailedEvent $event): void
    {
        $this->updateDeliveryStatus($event, 'failed');

        Log::warning('Webhook call failed', [
            'url' => $event->webhookUrl,
            'event_type' => $event->meta['event_type'] ?? 'unknown',
            'attempt' => $event->attempt,
            'response_status' => $event->response?->getStatusCode(),
            'uuid' => $event->uuid,
        ]);
    }

    /**
     * Handle final webhook call failed event.
     */
    public function handleFinalWebhookCallFailed(FinalWebhookCallFailedEvent $event): void
    {
        $this->updateDeliveryStatus($event, 'failed');

        Log::error('Final webhook call failed', [
            'url' => $event->webhookUrl,
            'event_type' => $event->meta['event_type'] ?? 'unknown',
            'attempt' => $event->attempt,
            'response_status' => $event->response?->getStatusCode(),
            'uuid' => $event->uuid,
        ]);
    }

    /**
     * Update the delivery status based on the webhook event.
     */
    private function updateDeliveryStatus(object $event, string $status): void
    {
        // 从 meta 数据中获取 delivery_id
        $deliveryId = $event->meta['delivery_id'] ?? null;

        if (! $deliveryId) {
            return;
        }

        $delivery = WebhookDelivery::find($deliveryId);

        if (! $delivery) {
            return;
        }

        $updateData = [
            'status' => $status,
            'attempts' => $event->attempt,
            'delivered_at' => now(),
        ];

        // 如果有响应，记录响应信息
        if ($event->response) {
            $updateData['response_status'] = $event->response->getStatusCode();
            $updateData['response_body'] = $event->response->getBody()->getContents();
            $updateData['response_headers'] = $event->response->getHeaders();
        }

        // 如果是失败事件，记录错误信息
        if ($status === 'failed' && property_exists($event, 'errorType')) {
            $updateData['error_message'] = $event->errorMessage ?? 'Unknown error';
        }

        $delivery->update($updateData);
    }
}
