<?php

namespace App\Notifications;

use App\Notifications\Channels\WebhookChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class WebhookNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private string $eventType,
        private array $payload
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return [WebhookChannel::class];
    }

    /**
     * Get the webhook representation of the notification.
     */
    public function toWebhook(object $notifiable): array
    {
        return [
            'event_type' => $this->eventType,
            'payload' => $this->payload,
        ];
    }
}
