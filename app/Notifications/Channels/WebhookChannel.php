<?php

namespace App\Notifications\Channels;

use App\Models\WebhookEndpoint;
use App\Models\Workspace;
use App\Service\WebhookService;
use Illuminate\Notifications\Notification;

class WebhookChannel
{
    public function __construct(
        private WebhookService $webhookService
    ) {}

    /**
     * Send the given notification.
     */
    public function send(object $notifiable, Notification $notification): void
    {
        // 获取 webhook 数据
        $webhookData = $notification->toWebhook($notifiable);

        if (! $webhookData || ! isset($webhookData['event_type'], $webhookData['payload'])) {
            return;
        }

        // 获取要发送的端点
        $endpoints = $this->getWebhookEndpoints($notifiable, $webhookData['event_type']);

        // 向每个端点发送 webhook
        foreach ($endpoints as $endpoint) {
            $this->webhookService->sendWebhook(
                $endpoint,
                $webhookData['event_type'],
                $webhookData['payload']
            );
        }
    }

    /**
     * 获取应该接收此通知的 webhook 端点
     */
    private function getWebhookEndpoints(object $notifiable, string $eventType): \Illuminate\Support\Collection
    {
        $query = WebhookEndpoint::where('is_active', true);

        // 根据 notifiable 类型确定查询条件
        if (method_exists($notifiable, 'getWebhookWorkspaceId')) {
            $workspaceId = $notifiable->getWebhookWorkspaceId();
            if ($workspaceId) {
                $query->where('workspace_id', $workspaceId);
            }
        } elseif (property_exists($notifiable, 'workspace_id')) {
            $query->where('workspace_id', $notifiable->workspace_id);
        } elseif (method_exists($notifiable, 'workspace')) {
            $workspace = $notifiable->workspace;
            if ($workspace) {
                $query->where('workspace_id', $workspace->id);
            }
        } elseif (get_class($notifiable) === Workspace::class) {
            $query->where('workspace_id', $notifiable->id);
        }

        return $query->get()->filter(function (WebhookEndpoint $endpoint) use ($eventType) {
            return $endpoint->shouldReceiveEvent($eventType) || $endpoint->shouldReceiveEvent('*');
        });
    }
}
