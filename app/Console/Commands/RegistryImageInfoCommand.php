<?php

namespace App\Console\Commands;

use App\Models\Workspace;
use App\Service\ImageValidationService;
use Illuminate\Console\Command;

class RegistryImageInfoCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'registry:image-info {image : The full image name (e.g., nginx:latest)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get information about a container image from a registry';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $image = $this->argument('image');
        $this->info("Fetching information for image: {$image}");

        try {
            // 创建一个临时的 Workspace 实例用于服务
            $workspace = new Workspace;
            $imageValidationService = new ImageValidationService($workspace);

            // 使用 ImageValidationService 获取镜像信息
            $result = $imageValidationService->validateImageSize($image);

            if (! $result['valid']) {
                $this->error('Error fetching image information: '.$result['error']);

                return 1;
            }

            $this->displayImageInfo($result);

        } catch (\Exception $e) {
            $this->error('Error fetching image information: '.$e->getMessage());

            return 1;
        }

        return 0;
    }

    private function displayImageInfo(array $result): void
    {
        $this->info('Image Layers:');
        $this->table(
            ['Digest', 'Size (Bytes)'],
            collect($result['layers'])->map(fn ($layer) => [
                $layer['digest'],
                $layer['size'],
            ])->toArray()
        );

        $this->info('Total Image Size: '.$result['size_mb'].' MB');
        $this->info('Total Layers: '.$result['layers_count']);
    }
}
