<?php

namespace App\DTOs;

use App\Traits\K8sResourceParsingTrait;
use Carbon\Carbon;

/**
 * Deployment DTO
 */
class DeploymentDTO extends KubernetesResourceDTO
{
    use K8sResourceParsingTrait;

    public function __construct(
        string $name,
        string $namespace,
        array $labels = [],
        array $annotations = [],
        ?Carbon $createdAt = null,
        ?string $uid = null,
        ?string $resourceVersion = null,
        public array $containers = [],
        public int $replicas = 1,
        public int $readyReplicas = 0,
        public int $availableReplicas = 0,
        public string $status = 'Unknown',
        public ?string $strategy = null,
        public array $conditions = [],
        public array $volumes = [],
        public array $imagePullSecrets = [],
        public string $restartPolicy = 'Always'
    ) {
        parent::__construct($name, $namespace, $labels, $annotations, $createdAt, $uid, $resourceVersion);
    }

    /**
     * 从 Kubernetes Deployment 资源创建 DTO
     */
    public static function fromK8sResource(array $resource): static
    {
        $metadata = $resource['metadata'] ?? [];
        $spec = $resource['spec'] ?? [];
        $status = $resource['status'] ?? [];

        // 解析容器信息和其他资源
        $containers = $spec['template']['spec']['containers'] ?? [];
        $podVolumes = $spec['template']['spec']['volumes'] ?? [];
        $podImagePullSecrets = $spec['template']['spec']['imagePullSecrets'] ?? [];

        return new static(
            name: $metadata['name'] ?? '',
            namespace: $metadata['namespace'] ?? '',
            labels: static::filterLabels($metadata['labels'] ?? []),
            annotations: static::filterAnnotations($metadata['annotations'] ?? []),
            createdAt: isset($metadata['creationTimestamp'])
                ? Carbon::parse($metadata['creationTimestamp'])
                : null,
            uid: $metadata['uid'] ?? null,
            resourceVersion: $metadata['resourceVersion'] ?? null,
            containers: static::extractContainers($containers, $podVolumes),
            replicas: $spec['replicas'] ?? 1,
            readyReplicas: $status['readyReplicas'] ?? 0,
            availableReplicas: $status['availableReplicas'] ?? 0,
            status: static::determineStatus($status),
            strategy: $spec['strategy']['type'] ?? null,
            conditions: $status['conditions'] ?? [],
            volumes: static::extractVolumes($podVolumes),
            imagePullSecrets: static::extractImagePullSecrets($podImagePullSecrets),
            restartPolicy: $spec['template']['spec']['restartPolicy'] ?? 'Always'
        );
    }

    protected static function determineStatus(array $status): string
    {
        $conditions = $status['conditions'] ?? [];

        foreach ($conditions as $condition) {
            if ($condition['type'] === 'Progressing' && $condition['status'] === 'True') {
                if ($condition['reason'] === 'NewReplicaSetAvailable') {
                    return 'Available';
                }
                if ($condition['reason'] === 'ReplicaSetUpdated') {
                    return 'Updating';
                }
            }

            if ($condition['type'] === 'Available' && $condition['status'] === 'False') {
                return 'Unavailable';
            }
        }

        $replicas = $status['replicas'] ?? 0;
        $readyReplicas = $status['readyReplicas'] ?? 0;

        if ($replicas === 0) {
            return 'Stopped';
        }

        if ($readyReplicas === $replicas) {
            return 'Running';
        }

        if ($readyReplicas > 0) {
            return 'Partially Ready';
        }

        return 'Pending';
    }

    /**
     * 获取资源类型
     */
    public function getResourceType(): string
    {
        return 'Deployment';
    }

    /**
     * 转换为数组
     */
    public function toArray(): array
    {
        // 提取所有容器的端口信息
        $ports = [];
        foreach ($this->containers as $container) {
            $containerPorts = $container['ports'] ?? [];
            foreach ($containerPorts as $port) {
                $ports[] = [
                    'container_name' => $container['name'] ?? '',
                    'port_name' => $port['name'] ?? 'default',
                    'port' => $port['container_port'] ?? $port['port'] ?? 0,
                    'protocol' => $port['protocol'] ?? 'TCP',
                ];
            }
        }

        return array_merge(parent::toArray(), [
            'type' => $this->getResourceType(),
            'containers' => $this->containers,
            'ports' => $ports,
            'replicas' => $this->replicas,
            'ready_replicas' => $this->readyReplicas,
            'available_replicas' => $this->availableReplicas,
            'status' => $this->status,
            'strategy' => $this->strategy,
            'conditions' => $this->conditions,
            'volumes' => $this->volumes,
            'image_pull_secrets' => $this->imagePullSecrets,
            'restart_policy' => $this->restartPolicy,
            'resource_version' => $this->resourceVersion,
            'created_at' => $this->createdAt?->toISOString(),
        ]);
    }
}
